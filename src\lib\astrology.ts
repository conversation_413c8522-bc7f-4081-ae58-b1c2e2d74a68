// Astrology Calculation Service
// Handahana (Birth Chart) calculations using Vedic Astrology principles
// Enhanced with Sri Lankan Vedic Horoscope Guide calculations

import { Origin, Horoscope } from 'circular-natal-horoscope-js';

// ===== SRI LANKAN VEDIC ASTROLOGY CALCULATIONS =====
// Based on Sri Lankan Vedic Horoscope Guide for 100% accuracy

/**
 * Sri Lankan Time Zone Conversion
 * Handle historical UTC offsets for Sri Lanka based on birth date
 */
export function getSriLankanUTCOffset(birthDate: Date): number {
  const year = birthDate.getFullYear();
  const month = birthDate.getMonth() + 1; // Convert to 1-based month
  const day = birthDate.getDate();

  // Before 25 May 1996: +05:30
  if (year < 1996 || (year === 1996 && month < 5) || (year === 1996 && month === 5 && day < 25)) {
    return 5.5; // +05:30
  }

  // 25 May 1996, 00:00 → 26 Oct 1996, 00:30: +06:30
  if (year === 1996 && month >= 5 && day >= 25 &&
      !(month === 10 && day >= 26)) {
    return 6.5; // +06:30
  }

  // 26 Oct 1996, 00:30 → 15 Apr 2006, 00:30: +06:00
  if ((year === 1996 && month === 10 && day >= 26) ||
      (year === 1996 && month > 10) ||  // Nov, Dec 1996
      (year > 1996 && year < 2006) ||
      (year === 2006 && month < 4) ||
      (year === 2006 && month === 4 && day < 15)) {
    return 6.0; // +06:00
  }

  // 15 Apr 2006, 00:30 onward: +05:30
  return 5.5; // +05:30
}

/**
 * Convert local Sri Lankan time to UTC
 */
export function convertToUTC(birthDate: Date, birthTime: string): Date {
  const [hours, minutes] = birthTime.split(':').map(Number);

  // Get appropriate UTC offset for the birth date
  const utcOffset = getSriLankanUTCOffset(birthDate);

  // Create UTC datetime directly by treating input as Sri Lankan local time
  const year = birthDate.getFullYear();
  const month = birthDate.getMonth();
  const day = birthDate.getDate();

  // Calculate UTC hours and minutes
  const totalMinutes = hours * 60 + minutes;
  const utcTotalMinutes = totalMinutes - (utcOffset * 60);

  let utcHours = Math.floor(utcTotalMinutes / 60);
  let utcMinutes = utcTotalMinutes % 60;
  let utcDay = day;
  let utcMonth = month;
  let utcYear = year;

  // Handle day rollover
  if (utcHours < 0) {
    utcHours += 24;
    utcDay -= 1;
    if (utcDay < 1) {
      utcMonth -= 1;
      if (utcMonth < 0) {
        utcMonth = 11;
        utcYear -= 1;
      }
      // Get last day of previous month
      utcDay = new Date(utcYear, utcMonth + 1, 0).getDate();
    }
  } else if (utcHours >= 24) {
    utcHours -= 24;
    utcDay += 1;
    const daysInMonth = new Date(utcYear, utcMonth + 1, 0).getDate();
    if (utcDay > daysInMonth) {
      utcDay = 1;
      utcMonth += 1;
      if (utcMonth > 11) {
        utcMonth = 0;
        utcYear += 1;
      }
    }
  }

  // Create UTC datetime
  const utcDateTime = new Date(Date.UTC(utcYear, utcMonth, utcDay, utcHours, utcMinutes, 0, 0));

  console.log('🕰️ Time conversion:', {
    inputDate: birthDate.toDateString(),
    inputTime: birthTime,
    sriLankanOffset: `+${utcOffset.toString().padStart(4, '0')}`,
    utcDateTime: utcDateTime.toISOString(),
    utcHours: utcDateTime.getUTCHours(),
    utcMinutes: utcDateTime.getUTCMinutes()
  });

  return utcDateTime;
}

/**
 * Calculate Julian Day from UTC date and time
 */
export function calculateJulianDay(utcDateTime: Date): number {
  const year = utcDateTime.getUTCFullYear();
  const month = utcDateTime.getUTCMonth() + 1; // Convert to 1-based month
  const day = utcDateTime.getUTCDate();
  const hour = utcDateTime.getUTCHours();
  const minute = utcDateTime.getUTCMinutes();
  const second = utcDateTime.getUTCSeconds();

  // Convert time to decimal hours
  const decimalHours = hour + minute/60 + second/3600;

  // Julian Day calculation algorithm
  let a = Math.floor((14 - month) / 12);
  let y = year + 4800 - a;
  let m = month + 12 * a - 3;

  let jdn = day + Math.floor((153 * m + 2) / 5) + 365 * y + Math.floor(y / 4) - Math.floor(y / 100) + Math.floor(y / 400) - 32045;

  // Add time fraction
  let jd = jdn + (decimalHours - 12) / 24;

  console.log('📅 Julian Day calculation:', {
    utcDateTime: utcDateTime.toISOString(),
    year, month, day, decimalHours,
    julianDay: jd
  });

  return jd;
}

/**
 * Calculate Lahiri Ayanamsa for given Julian Day
 * This is a simplified calculation - for production, use precise ephemeris data
 */
export function calculateLahiriAyanamsa(julianDay: number): number {
  // Lahiri Ayanamsa calculation (simplified formula)
  // For precise calculations, use Swiss Ephemeris or similar
  const t = (julianDay - 2451545.0) / 36525.0; // Julian centuries from J2000.0

  // Simplified Lahiri Ayanamsa formula (approximate)
  let ayanamsa = 23.85 + 0.013944 * t * 100; // Rough approximation

  // More accurate formula would require complex calculations
  // This is a placeholder - in production, use precise ephemeris
  ayanamsa = 24.0; // Fixed value for now - should be calculated precisely

  console.log('🌌 Lahiri Ayanamsa:', {
    julianDay,
    t,
    ayanamsa
  });

  return ayanamsa;
}

/**
 * Calculate Greenwich Sidereal Time
 */
export function calculateGreenwichSiderealTime(julianDay: number): number {
  const t = (julianDay - 2451545.0) / 36525.0;

  // Greenwich Mean Sidereal Time at 0h UT
  let gmst = 280.46061837 + 360.98564736629 * (julianDay - 2451545.0) + 0.000387933 * t * t - t * t * t / 38710000.0;

  // Normalize to 0-360 degrees
  gmst = ((gmst % 360) + 360) % 360;

  console.log('🌍 Greenwich Sidereal Time:', {
    julianDay,
    t,
    gmst
  });

  return gmst;
}

/**
 * Calculate Local Sidereal Time
 */
export function calculateLocalSiderealTime(julianDay: number, longitude: number): number {
  const gst = calculateGreenwichSiderealTime(julianDay);
  const lst = gst + longitude / 15.0; // Convert longitude to time

  // Normalize to 0-24 hours
  const normalizedLst = ((lst / 15.0) % 24 + 24) % 24;

  console.log('🏠 Local Sidereal Time:', {
    gst,
    longitude,
    lst,
    normalizedLst
  });

  return normalizedLst;
}

/**
 * Calculate Ascendant (Lagna) degree using proper spherical trigonometry
 * Based on Sri Lankan Vedic astrology standards
 */
export function calculateAscendant(localSiderealTime: number, latitude: number): number {
  // Convert LST from hours to degrees
  const lstDegrees = localSiderealTime * 15.0;

  // Convert to radians
  const latRad = latitude * Math.PI / 180.0;
  const lstRad = lstDegrees * Math.PI / 180.0;

  // Obliquity of ecliptic (approximately 23.44 degrees)
  const obliquity = 23.4392911 * Math.PI / 180.0;

  // Calculate ascendant using proper spherical trigonometry
  // Formula: tan(Asc) = cos(LST) / (sin(LST) * cos(obliquity) - tan(latitude) * sin(obliquity))
  const numerator = Math.cos(lstRad);
  const denominator = Math.sin(lstRad) * Math.cos(obliquity) - Math.tan(latRad) * Math.sin(obliquity);

  let ascendant = Math.atan2(numerator, denominator) * 180.0 / Math.PI;

  // Normalize to 0-360 degrees
  ascendant = ((ascendant % 360) + 360) % 360;

  // Additional correction for quadrant
  if (denominator < 0) {
    ascendant = (ascendant + 180) % 360;
  }

  console.log('🚐 Ascendant calculation (improved):', {
    localSiderealTime,
    latitude,
    lstDegrees,
    obliquity: obliquity * 180 / Math.PI,
    ascendant
  });

  return ascendant;
}

/**
 * Get tropical planetary positions (placeholder)
 * In production, this should use Swiss Ephemeris or similar
 */
export function getTropicalPlanetaryPositions(julianDay: number): { [planet: string]: number } {
  // This is a placeholder - in production, use precise ephemeris calculations
  // For now, we'll use the existing library to get tropical positions
  console.log('🌍 Getting tropical planetary positions for JD:', julianDay);

  // Placeholder values - should be calculated from ephemeris
  return {
    'Sun': 0,
    'Moon': 0,
    'Mars': 0,
    'Mercury': 0,
    'Jupiter': 0,
    'Venus': 0,
    'Saturn': 0,
    'Rahu': 0,
    'Ketu': 0
  };
}

/**
 * Convert tropical longitude to sidereal longitude
 */
export function tropicalToSidereal(tropicalLongitude: number, ayanamsa: number): number {
  const siderealLongitude = ((tropicalLongitude - ayanamsa) % 360 + 360) % 360;

  console.log('🌌 Tropical to Sidereal conversion:', {
    tropicalLongitude,
    ayanamsa,
    siderealLongitude
  });

  return siderealLongitude;
}

/**
 * Calculate sidereal planetary positions
 */
export function calculateSiderealPlanetaryPositions(julianDay: number): { [planet: string]: number } {
  const ayanamsa = calculateLahiriAyanamsa(julianDay);
  const tropicalPositions = getTropicalPlanetaryPositions(julianDay);

  const siderealPositions: { [planet: string]: number } = {};

  for (const [planet, tropicalLong] of Object.entries(tropicalPositions)) {
    siderealPositions[planet] = tropicalToSidereal(tropicalLong, ayanamsa);
  }

  console.log('🌟 Sidereal planetary positions:', siderealPositions);

  return siderealPositions;
}

/**
 * Determine Lagna Rashi from Ascendant degree
 */
export function getLagnaRashi(ascendantDegree: number): { rashi: string, rashiNumber: number } {
  const rashiNumber = Math.floor(ascendantDegree / 30) + 1;
  const rashiNames = [
    'Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
    'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'
  ];

  const rashi = rashiNames[rashiNumber - 1] || 'Unknown';

  console.log('🏠 Lagna Rashi:', {
    ascendantDegree,
    rashiNumber,
    rashi
  });

  return { rashi, rashiNumber };
}

/**
 * Calculate house cusps (30° apart from Ascendant)
 */
export function calculateHouseCusps(ascendantDegree: number): number[] {
  const cusps: number[] = [];

  for (let house = 1; house <= 12; house++) {
    const cusp = ((ascendantDegree + 30 * (house - 1)) % 360 + 360) % 360;
    cusps.push(cusp);
  }

  console.log('🏠 House cusps:', cusps);

  return cusps;
}

/**
 * Determine which house a planet is in based on its sidereal longitude
 */
export function getPlanetHouse(planetLongitude: number, ascendantDegree: number): number {
  // Normalize longitudes
  const normalizedPlanet = ((planetLongitude % 360) + 360) % 360;
  const normalizedAsc = ((ascendantDegree % 360) + 360) % 360;

  // Calculate relative position from ascendant
  let relativePosition = normalizedPlanet - normalizedAsc;
  if (relativePosition < 0) relativePosition += 360;

  // Determine house (1-12)
  const house = Math.floor(relativePosition / 30) + 1;

  console.log('🏠 Planet house placement:', {
    planetLongitude: normalizedPlanet,
    ascendantDegree: normalizedAsc,
    relativePosition,
    house
  });

  return house;
}

/**
 * Calculate Navamsa (D-9) position using standard mathematical method
 * Simple formula: Each sign is divided into 9 navamsas of 3°20' each
 */
export function calculateNavamsaPositionNew(longitude: number): { navamsaSign: number, navamsaLongitude: number } {
  console.log('🕉️ Calculating Navamsa using standard method for longitude:', longitude);

  // Normalize longitude to 0-360 range
  const normalizedLongitude = ((longitude % 360) + 360) % 360;

  // Each navamsa is 3°20' = 3.333... degrees = 10/3 degrees
  const navamsaWidth = 10.0 / 3.0;

  // Calculate which navamsa (0-107, since 12 signs × 9 navamsas = 108)
  const navamsaNumber = Math.floor(normalizedLongitude / navamsaWidth);

  // The navamsa sign is the navamsa number modulo 12
  const navamsaSign = navamsaNumber % 12;

  // Calculate position within the navamsa sign
  const degreeInNavamsa = normalizedLongitude % navamsaWidth;
  const navamsaLongitude = navamsaSign * 30 + degreeInNavamsa * 9;

  console.log('🕉️ Standard Navamsa calculation:', {
    originalLongitude: longitude,
    normalizedLongitude: normalizedLongitude.toFixed(6),
    navamsaWidth: navamsaWidth.toFixed(6),
    navamsaNumber: navamsaNumber,
    navamsaSign: navamsaSign,
    navamsaSignName: VEDIC_SIGNS[navamsaSign],
    degreeInNavamsa: degreeInNavamsa.toFixed(6),
    navamsaLongitude: navamsaLongitude.toFixed(6)
  });

  return {
    navamsaSign: navamsaSign,
    navamsaLongitude
  };
}

/**
 * Calculate Nakshatra and Pada from Moon's sidereal longitude
 * Each nakshatra = 13°20′ (≈13.333…°)
 */
export function calculateNakshatraAndPada(moonLongitude: number): {
  nakshatraIndex: number,
  nakshatraName: string,
  pada: number
} {
  console.log('🌙 Calculating Nakshatra and Pada for Moon longitude:', moonLongitude);

  // Normalize longitude
  const normalizedLongitude = ((moonLongitude % 360) + 360) % 360;

  // Each nakshatra spans 13.333... degrees
  const nakshatraSpan = 13.333333333333334; // 360/27

  // Nakshatra index (0-based)
  const nakshatraIndex = Math.floor(normalizedLongitude / nakshatraSpan);

  // Fraction within nakshatra
  const fractionInNakshatra = (normalizedLongitude % nakshatraSpan) / nakshatraSpan;

  // Pada (1-4)
  const pada = Math.floor(fractionInNakshatra * 4) + 1;

  // Nakshatra names
  const nakshatraNames = [
    'Ashwini', 'Bharani', 'Krittika', 'Rohini', 'Mrigashira', 'Ardra',
    'Punarvasu', 'Pushya', 'Ashlesha', 'Magha', 'Purva Phalguni', 'Uttara Phalguni',
    'Hasta', 'Chitra', 'Swati', 'Vishakha', 'Anuradha', 'Jyeshtha',
    'Mula', 'Purva Ashadha', 'Uttara Ashadha', 'Shravana', 'Dhanishta', 'Shatabhisha',
    'Purva Bhadrapada', 'Uttara Bhadrapada', 'Revati'
  ];

  const nakshatraName = nakshatraNames[nakshatraIndex] || 'Unknown';

  console.log('🌙 Nakshatra and Pada calculation:', {
    normalizedLongitude,
    nakshatraSpan,
    nakshatraIndex,
    nakshatraName,
    fractionInNakshatra,
    pada
  });

  return {
    nakshatraIndex,
    nakshatraName,
    pada
  };
}

/**
 * Calculate Tithi from Moon and Sun sidereal longitudes
 * T = (L_Moon - L_Sun + 360) mod 360
 * Tithi = ⌊T/12⌋ + 1
 */
export function calculateTithi(moonLongitude: number, sunLongitude: number): {
  tithiNumber: number,
  tithiName: string
} {
  console.log('🌞 Calculating Tithi:', { moonLongitude, sunLongitude });

  // T = (L_Moon - L_Sun + 360) mod 360
  const T = ((moonLongitude - sunLongitude + 360) % 360 + 360) % 360;

  // Tithi = ⌊T/12⌋ + 1
  const tithiNumber = Math.floor(T / 12) + 1;

  // Tithi names
  const tithiNames = [
    'Pratipada', 'Dwitiya', 'Tritiya', 'Chaturthi', 'Panchami', 'Shashthi',
    'Saptami', 'Ashtami', 'Navami', 'Dashami', 'Ekadashi', 'Dwadashi',
    'Trayodashi', 'Chaturdashi', 'Purnima/Amavasya'
  ];

  const tithiName = tithiNames[Math.min(tithiNumber - 1, 14)] || 'Unknown';

  console.log('🌞 Tithi calculation:', {
    T,
    tithiNumber,
    tithiName
  });

  return {
    tithiNumber,
    tithiName
  };
}

/**
 * Vimshottari Dasha System Implementation
 * Based on Sri Lankan Vedic Horoscope Guide
 */

// Nakshatra lords in order
const NAKSHATRA_LORDS_NEW = [
  'Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury', // 1-9
  'Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury', // 10-18
  'Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury'  // 19-27
];

// Full dasha durations in years
const DASHA_PERIODS_NEW = {
  'Ketu': 7,
  'Venus': 20,
  'Sun': 6,
  'Moon': 10,
  'Mars': 7,
  'Rahu': 18,
  'Jupiter': 16,
  'Saturn': 19,
  'Mercury': 17
};

/**
 * Calculate Vimshottari Dasha according to the guide
 */
export function calculateVimshottariDashaNew(moonLongitude: number, birthDate: Date): {
  currentDasha: string;
  balance: string;
  periods: Array<{
    planet: string;
    startDate: Date;
    endDate: Date;
    years: number;
    subDashas?: Array<{
      planet: string;
      startDate: Date;
      endDate: Date;
      duration: number;
    }>;
  }>;
} {
  console.log('⏳ Calculating Vimshottari Dasha:', { moonLongitude, birthDate });

  // 1. Find birth nakshatra lord
  const { nakshatraIndex, nakshatraName } = calculateNakshatraAndPada(moonLongitude);
  const nakshatraLord = NAKSHATRA_LORDS_NEW[nakshatraIndex];

  console.log('🌟 Birth nakshatra details:', {
    nakshatraIndex,
    nakshatraName,
    nakshatraLord
  });

  // 2. Calculate remaining arc in nakshatra
  const nakshatraSpan = 13.333333333333334; // 360/27
  const remainingArc = nakshatraSpan - (moonLongitude % nakshatraSpan);

  console.log('📐 Remaining arc calculation:', {
    nakshatraSpan,
    moonLongitudeInNakshatra: moonLongitude % nakshatraSpan,
    remainingArc
  });

  // 3. Calculate mahadasha balance in years
  const lordDashaPeriod = DASHA_PERIODS_NEW[nakshatraLord as keyof typeof DASHA_PERIODS_NEW];
  const balanceYears = (remainingArc / nakshatraSpan) * lordDashaPeriod;

  console.log('⏱️ Mahadasha balance:', {
    lordDashaPeriod,
    balanceYears
  });

  // 4. Build dasha sequence starting from birth nakshatra lord
  const dashaPlanets = ['Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury'];
  const startIndex = dashaPlanets.indexOf(nakshatraLord);

  const periods = [];
  let currentDate = new Date(birthDate);

  // First period (current dasha with balance)
  const firstEndDate = new Date(currentDate);
  firstEndDate.setFullYear(firstEndDate.getFullYear() + Math.floor(balanceYears));
  firstEndDate.setMonth(firstEndDate.getMonth() + Math.floor((balanceYears % 1) * 12));

  periods.push({
    planet: nakshatraLord,
    startDate: new Date(currentDate),
    endDate: new Date(firstEndDate),
    years: balanceYears,
    subDashas: calculateSubDashas(nakshatraLord, balanceYears, new Date(currentDate))
  });

  currentDate = new Date(firstEndDate);

  // Remaining periods (full cycles)
  for (let i = 1; i < 9; i++) {
    const planetIndex = (startIndex + i) % 9;
    const planet = dashaPlanets[planetIndex];
    const years = DASHA_PERIODS_NEW[planet as keyof typeof DASHA_PERIODS_NEW];

    const endDate = new Date(currentDate);
    endDate.setFullYear(endDate.getFullYear() + years);

    periods.push({
      planet,
      startDate: new Date(currentDate),
      endDate: new Date(endDate),
      years,
      subDashas: calculateSubDashas(planet, years, new Date(currentDate))
    });

    currentDate = new Date(endDate);
  }

  // Format balance string
  const balanceString = `${Math.floor(balanceYears)} years ${Math.floor((balanceYears % 1) * 12)} months`;

  console.log('✅ Vimshottari Dasha calculation completed:', {
    currentDasha: nakshatraLord,
    balance: balanceString,
    periodsCount: periods.length
  });

  return {
    currentDasha: nakshatraLord,
    balance: balanceString,
    periods
  };
}

/**
 * Calculate sub-dashas (Antar/Bhukti) for a mahadasha
 * Formula: Bhukti_planet = (MahadashaYears × BhuktiPlanetYears) / 120 (in years)
 */
function calculateSubDashas(mahadashaPlanet: string, mahadashaYears: number, startDate: Date) {
  const dashaPlanets = ['Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury'];
  const startIndex = dashaPlanets.indexOf(mahadashaPlanet);

  const subDashas = [];
  let currentDate = new Date(startDate);

  for (let i = 0; i < 9; i++) {
    const planetIndex = (startIndex + i) % 9;
    const planet = dashaPlanets[planetIndex];
    const bhuktiPlanetYears = DASHA_PERIODS_NEW[planet as keyof typeof DASHA_PERIODS_NEW];

    // Calculate bhukti duration
    const bhuktiDuration = (mahadashaYears * bhuktiPlanetYears) / 120;

    const endDate = new Date(currentDate);
    endDate.setFullYear(endDate.getFullYear() + Math.floor(bhuktiDuration));
    endDate.setMonth(endDate.getMonth() + Math.floor((bhuktiDuration % 1) * 12));
    endDate.setDate(endDate.getDate() + Math.floor(((bhuktiDuration % 1) * 12 % 1) * 30));

    subDashas.push({
      planet,
      startDate: new Date(currentDate),
      endDate: new Date(endDate),
      duration: bhuktiDuration
    });

    currentDate = new Date(endDate);
  }

  return subDashas;
}

/**
 * Comprehensive Yoga and Dosha Analysis
 * Based on Sri Lankan Vedic Horoscope Guide
 */

export interface YogaAnalysisNew {
  beneficYogas: Array<{
    name: string;
    description: string;
    present: boolean;
    strength: 'Weak' | 'Medium' | 'Strong';
  }>;
  maleficYogas: Array<{
    name: string;
    description: string;
    present: boolean;
    severity: 'Low' | 'Medium' | 'High';
  }>;
}

export interface DoshaAnalysisNew {
  kujaDosha: {
    present: boolean;
    severity: 'None' | 'Low' | 'Medium' | 'High';
    houses: number[];
    remedies: string[];
  };
  kalaSarpaYoga: {
    present: boolean;
    type: string;
    description: string;
  };
  grahanDosha: {
    present: boolean;
    planets: string[];
    description: string;
  };
  shakataYoga: {
    present: boolean;
    description: string;
  };
  kemadruma: {
    present: boolean;
    description: string;
  };
}

/**
 * Calculate comprehensive Yoga analysis
 */
export function calculateYogaAnalysisNew(
  planetPositions: { [planet: string]: { longitude: number; house: number } },
  ascendantDegree: number
): YogaAnalysisNew {
  console.log('🧘 Calculating comprehensive Yoga analysis...');

  const beneficYogas = [];
  const maleficYogas = [];

  // Get planet houses
  const planetHouses: { [planet: string]: number } = {};
  for (const [planet, position] of Object.entries(planetPositions)) {
    planetHouses[planet] = position.house;
  }

  // 1. Gaja Kesari Yoga - Moon and Jupiter in Kendra (1, 4, 7, 10)
  const moonHouse = planetHouses['Moon'] || 0;
  const jupiterHouse = planetHouses['Jupiter'] || 0;
  const kendraHouses = [1, 4, 7, 10];

  const gajaKesariPresent = kendraHouses.includes(moonHouse) && kendraHouses.includes(jupiterHouse);
  beneficYogas.push({
    name: 'Gaja Kesari Yoga',
    description: 'Moon and Jupiter in Kendra houses - brings wisdom, wealth, and respect',
    present: gajaKesariPresent,
    strength: gajaKesariPresent ? 'Strong' : 'Weak'
  });

  // 2. Budha-Aditya Yoga - Sun and Mercury in same house and sign
  const sunHouse = planetHouses['Sun'] || 0;
  const mercuryHouse = planetHouses['Mercury'] || 0;
  const sunLongitude = planetPositions['Sun']?.longitude || 0;
  const mercuryLongitude = planetPositions['Mercury']?.longitude || 0;

  const budhaAdityaPresent = sunHouse === mercuryHouse &&
    Math.floor(sunLongitude / 30) === Math.floor(mercuryLongitude / 30);

  beneficYogas.push({
    name: 'Budha-Aditya Yoga',
    description: 'Sun and Mercury conjunction - enhances intelligence and communication',
    present: budhaAdityaPresent,
    strength: budhaAdityaPresent ? 'Medium' : 'Weak'
  });

  // 3. Dhana Yogas - involving 2nd, 5th, 9th, 11th lords (simplified check)
  // This would require house lordship calculations - placeholder for now
  beneficYogas.push({
    name: 'Dhana Yoga',
    description: 'Wealth-giving planetary combinations',
    present: false, // Placeholder - requires complex lordship analysis
    strength: 'Weak'
  });

  // 4. Raj Yogas - Lords of Kendra and Trikona in mutual conjunction
  // This would require house lordship calculations - placeholder for now
  beneficYogas.push({
    name: 'Raj Yoga',
    description: 'Royal combinations bringing power and status',
    present: false, // Placeholder - requires complex lordship analysis
    strength: 'Weak'
  });

  console.log('✅ Yoga analysis completed:', {
    beneficYogasCount: beneficYogas.filter(y => y.present).length,
    maleficYogasCount: maleficYogas.filter(y => y.present).length
  });

  return {
    beneficYogas,
    maleficYogas
  };
}

/**
 * Calculate comprehensive Dosha analysis
 */
export function calculateDoshaAnalysisNew(
  planetPositions: { [planet: string]: { longitude: number; house: number } },
  ascendantDegree: number
): DoshaAnalysisNew {
  console.log('⚠️ Calculating comprehensive Dosha analysis...');

  // Get planet houses
  const planetHouses: { [planet: string]: number } = {};
  for (const [planet, position] of Object.entries(planetPositions)) {
    planetHouses[planet] = position.house;
  }

  // 1. Kuja Dosha (Manglik) - Mars in 1st, 4th, 7th, 8th, or 12th house
  const marsHouse = planetHouses['Mars'] || 0;
  const kujaDoshaHouses = [1, 4, 7, 8, 12];
  const kujaDoshaPresent = kujaDoshaHouses.includes(marsHouse);

  let kujaSeverity: 'None' | 'Low' | 'Medium' | 'High' = 'None';
  if (kujaDoshaPresent) {
    if ([1, 7, 8].includes(marsHouse)) {
      kujaSeverity = 'High';
    } else if ([4, 12].includes(marsHouse)) {
      kujaSeverity = 'Medium';
    }
  }

  // 2. Kala Sarpa Yoga - All planets between Rahu and Ketu axis
  const rahuHouse = planetHouses['Rahu'] || 0;
  const ketuHouse = planetHouses['Ketu'] || 0;

  // Check if all other planets are between Rahu and Ketu
  const otherPlanets = ['Sun', 'Moon', 'Mars', 'Mercury', 'Jupiter', 'Venus', 'Saturn'];
  let kalaSarpaPresent = true;

  for (const planet of otherPlanets) {
    const planetHouse = planetHouses[planet] || 0;
    // Simplified check - in reality, this requires longitude-based calculation
    if (planetHouse !== 0) {
      // Check if planet is between Rahu and Ketu (simplified)
      const isInRange = (rahuHouse < ketuHouse) ?
        (planetHouse > rahuHouse && planetHouse < ketuHouse) :
        (planetHouse > rahuHouse || planetHouse < ketuHouse);

      if (!isInRange) {
        kalaSarpaPresent = false;
        break;
      }
    }
  }

  // 3. Grahan Dosha - Sun or Moon conjunction with Rahu/Ketu
  const sunHouse = planetHouses['Sun'] || 0;
  const moonHouse = planetHouses['Moon'] || 0;

  const grahanDoshaPresent = (sunHouse === rahuHouse || sunHouse === ketuHouse ||
                             moonHouse === rahuHouse || moonHouse === ketuHouse);

  const grahanPlanets = [];
  if (sunHouse === rahuHouse || sunHouse === ketuHouse) grahanPlanets.push('Sun');
  if (moonHouse === rahuHouse || moonHouse === ketuHouse) grahanPlanets.push('Moon');

  // 4. Shakata Yoga - Moon in 6th or 8th from Jupiter
  const moonJupiterDistance = Math.abs(moonHouse - (planetHouses['Jupiter'] || 0));
  const shakataPresent = moonJupiterDistance === 6 || moonJupiterDistance === 8;

  // 5. Kemadruma Dosha - Moon isolated with no planets in 2nd or 12th house
  const moonPrevHouse = moonHouse === 1 ? 12 : moonHouse - 1;
  const moonNextHouse = moonHouse === 12 ? 1 : moonHouse + 1;

  let kemadruma = true;
  for (const planet of otherPlanets) {
    const planetHouse = planetHouses[planet] || 0;
    if (planetHouse === moonPrevHouse || planetHouse === moonNextHouse) {
      kemadruma = false;
      break;
    }
  }

  console.log('✅ Dosha analysis completed:', {
    kujaDosha: kujaDoshaPresent,
    kalaSarpaYoga: kalaSarpaPresent,
    grahanDosha: grahanDoshaPresent,
    shakataYoga: shakataPresent,
    kemadruma
  });

  return {
    kujaDosha: {
      present: kujaDoshaPresent,
      severity: kujaSeverity,
      houses: kujaDoshaPresent ? [marsHouse] : [],
      remedies: kujaDoshaPresent ? [
        'Recite Hanuman Chalisa daily',
        'Wear red coral gemstone',
        'Fast on Tuesdays',
        'Donate red items on Tuesdays'
      ] : []
    },
    kalaSarpaYoga: {
      present: kalaSarpaPresent,
      type: kalaSarpaPresent ? 'Full Kala Sarpa' : 'None',
      description: kalaSarpaPresent ?
        'All planets positioned between Rahu and Ketu axis' :
        'No Kala Sarpa Yoga present'
    },
    grahanDosha: {
      present: grahanDoshaPresent,
      planets: grahanPlanets,
      description: grahanDoshaPresent ?
        `Eclipse condition affecting ${grahanPlanets.join(', ')}` :
        'No Grahan Dosha present'
    },
    shakataYoga: {
      present: shakataPresent,
      description: shakataPresent ?
        'Moon in 6th or 8th from Jupiter - may cause instability' :
        'No Shakata Yoga present'
    },
    kemadruma: {
      present: kemadruma,
      description: kemadruma ?
        'Moon isolated without planetary support - may cause emotional challenges' :
        'No Kemadruma Dosha present'
    }
  };
}

/**
 * Graha Bala (Planetary Strength) Calculations
 * Based on Sri Lankan Vedic Horoscope Guide
 */

export interface GrahaBalaNew {
  [planet: string]: {
    ucchaBala: number; // Exaltation strength
    swaRashi: boolean; // Own sign
    debilitation: boolean; // Debilitated
    overallStrength: 'Very Weak' | 'Weak' | 'Medium' | 'Strong' | 'Very Strong';
    description: string;
  };
}

// Exaltation degrees for planets
const EXALTATION_DEGREES = {
  'Sun': { sign: 0, degree: 10 }, // Aries 10°
  'Moon': { sign: 1, degree: 3 }, // Taurus 3°
  'Mars': { sign: 9, degree: 28 }, // Capricorn 28°
  'Mercury': { sign: 5, degree: 15 }, // Virgo 15°
  'Jupiter': { sign: 3, degree: 5 }, // Cancer 5°
  'Venus': { sign: 11, degree: 27 }, // Pisces 27°
  'Saturn': { sign: 6, degree: 20 }, // Libra 20°
  'Rahu': { sign: 1, degree: 20 }, // Taurus 20° (some traditions)
  'Ketu': { sign: 7, degree: 20 }  // Scorpio 20° (some traditions)
};

// Debilitation degrees (opposite to exaltation)
const DEBILITATION_DEGREES = {
  'Sun': { sign: 6, degree: 10 }, // Libra 10°
  'Moon': { sign: 7, degree: 3 }, // Scorpio 3°
  'Mars': { sign: 3, degree: 28 }, // Cancer 28°
  'Mercury': { sign: 11, degree: 15 }, // Pisces 15°
  'Jupiter': { sign: 9, degree: 5 }, // Capricorn 5°
  'Venus': { sign: 5, degree: 27 }, // Virgo 27°
  'Saturn': { sign: 0, degree: 20 }, // Aries 20°
  'Rahu': { sign: 7, degree: 20 }, // Scorpio 20°
  'Ketu': { sign: 1, degree: 20 }  // Taurus 20°
};

// Own signs for planets
const OWN_SIGNS = {
  'Sun': [4], // Leo
  'Moon': [3], // Cancer
  'Mars': [0, 7], // Aries, Scorpio
  'Mercury': [2, 5], // Gemini, Virgo
  'Jupiter': [8, 11], // Sagittarius, Pisces
  'Venus': [1, 6], // Taurus, Libra
  'Saturn': [9, 10], // Capricorn, Aquarius
  'Rahu': [], // No own signs
  'Ketu': [] // No own signs
};

/**
 * Calculate Graha Bala (Planetary Strength)
 */
export function calculateGrahaBalaNew(
  planetPositions: { [planet: string]: { longitude: number; house: number } }
): GrahaBalaNew {
  console.log('💪 Calculating Graha Bala (Planetary Strength)...');

  const grahaBala: GrahaBalaNew = {};

  for (const [planet, position] of Object.entries(planetPositions)) {
    const longitude = position.longitude;
    const signIndex = Math.floor(longitude / 30);
    const degreeInSign = longitude % 30;

    // Calculate Uccha Bala (Exaltation strength)
    let ucchaBala = 0;
    const exaltationInfo = EXALTATION_DEGREES[planet as keyof typeof EXALTATION_DEGREES];
    if (exaltationInfo) {
      if (signIndex === exaltationInfo.sign) {
        // Calculate strength based on proximity to exact exaltation degree
        const distanceFromExaltation = Math.abs(degreeInSign - exaltationInfo.degree);
        ucchaBala = Math.max(0, 100 - (distanceFromExaltation * 3.33)); // Max 100, decreases with distance
      }
    }

    // Check if in own sign (Swa Rashi)
    const ownSigns = OWN_SIGNS[planet as keyof typeof OWN_SIGNS] || [];
    const swaRashi = ownSigns.includes(signIndex);

    // Check if debilitated
    let debilitation = false;
    const debilitationInfo = DEBILITATION_DEGREES[planet as keyof typeof DEBILITATION_DEGREES];
    if (debilitationInfo) {
      if (signIndex === debilitationInfo.sign) {
        const distanceFromDebilitation = Math.abs(degreeInSign - debilitationInfo.degree);
        debilitation = distanceFromDebilitation < 5; // Within 5 degrees of exact debilitation
      }
    }

    // Calculate overall strength
    let overallStrength: 'Very Weak' | 'Weak' | 'Medium' | 'Strong' | 'Very Strong';
    let description: string;

    if (debilitation) {
      overallStrength = 'Very Weak';
      description = `${planet} is debilitated in ${getSignName(signIndex)} - very weak position`;
    } else if (ucchaBala > 80) {
      overallStrength = 'Very Strong';
      description = `${planet} is exalted in ${getSignName(signIndex)} - very strong position`;
    } else if (swaRashi) {
      overallStrength = 'Strong';
      description = `${planet} is in own sign ${getSignName(signIndex)} - strong position`;
    } else if (ucchaBala > 50) {
      overallStrength = 'Strong';
      description = `${planet} has good strength in ${getSignName(signIndex)}`;
    } else if (ucchaBala > 25) {
      overallStrength = 'Medium';
      description = `${planet} has moderate strength in ${getSignName(signIndex)}`;
    } else {
      overallStrength = 'Weak';
      description = `${planet} has weak strength in ${getSignName(signIndex)}`;
    }

    grahaBala[planet] = {
      ucchaBala,
      swaRashi,
      debilitation,
      overallStrength,
      description
    };

    console.log(`💪 ${planet} strength:`, {
      sign: getSignName(signIndex),
      ucchaBala,
      swaRashi,
      debilitation,
      overallStrength
    });
  }

  console.log('✅ Graha Bala calculation completed');

  return grahaBala;
}

/**
 * Get sign name from index
 */
function getSignName(signIndex: number): string {
  const signs = [
    'Aries', 'Taurus', 'Gemini', 'Cancer', 'Leo', 'Virgo',
    'Libra', 'Scorpio', 'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'
  ];
  return signs[signIndex] || 'Unknown';
}

/**
 * Sri Lankan Vedic Horoscope Output Structure
 * Based on the guide's final output structure
 */
export interface SriLankanHoroscopeData {
  utc_birth: string;
  julianday: number;
  lagna: {
    degree: number;
    rashi: string;
  };
  rashi_chart: {
    [house: string]: string[];
  };
  navamsa_chart: {
    [house: string]: string[];
  };
  nakshatra: {
    index: number;
    name: string;
    pada: number;
  };
  tithi: number;
  vimshottari: {
    start: string;
    lord: string;
    remaining_years: number;
    sub_dashas: Array<{
      planet: string;
      start_date: string;
      end_date: string;
      duration_years: number;
    }>;
  };
  yogas: string[];
  doshas: string[];
  graha_bala: {
    [planet: string]: {
      strength: number;
      status: string;
    };
  };
}

/**
 * Main Sri Lankan Vedic Horoscope Calculation Function
 * Implements the complete calculation system from the guide
 */
export async function calculateSriLankanHoroscope(birthDetails: BirthDetails): Promise<SriLankanHoroscopeData> {
  console.log('🇱🇰 Starting Sri Lankan Vedic Horoscope calculation...');
  console.log('📊 Birth details:', birthDetails);

  try {
    // Step 1: Convert to UTC using Sri Lankan historical offsets
    const utcDateTime = convertToUTC(birthDetails.birthDate, birthDetails.birthTime);
    console.log('✅ Step 1: UTC conversion completed');

    // Step 2: Calculate Julian Day
    const julianDay = calculateJulianDay(utcDateTime);
    console.log('✅ Step 2: Julian Day calculated');

    // Step 3: Calculate sidereal planetary positions
    const siderealPositions = calculateSiderealPlanetaryPositions(julianDay);
    console.log('✅ Step 3: Sidereal positions calculated');

    // Step 4: Calculate Ascendant (Lagna)
    const localSiderealTime = calculateLocalSiderealTime(julianDay, birthDetails.longitude);
    const ascendantDegree = calculateAscendant(localSiderealTime, birthDetails.latitude);
    const lagnaRashi = getLagnaRashi(ascendantDegree);
    console.log('✅ Step 4: Ascendant calculated');

    // Step 5: Calculate house cusps and planet placements
    const houseCusps = calculateHouseCusps(ascendantDegree);
    const planetHouses: { [planet: string]: { longitude: number; house: number } } = {};

    for (const [planet, longitude] of Object.entries(siderealPositions)) {
      const house = getPlanetHouse(longitude, ascendantDegree);
      planetHouses[planet] = { longitude, house };
    }
    console.log('✅ Step 5: House placements calculated');

    // Step 6: Calculate Navamsa chart
    const navamsaChart: { [house: string]: string[] } = {};
    for (let i = 1; i <= 12; i++) {
      navamsaChart[`house${i}`] = [];
    }

    for (const [planet, position] of Object.entries(planetHouses)) {
      const navamsa = calculateNavamsaPositionNew(position.longitude);
      const navamsaHouse = navamsa.navamsaSign + 1;
      navamsaChart[`house${navamsaHouse}`].push(planet);
    }
    console.log('✅ Step 6: Navamsa chart calculated');

    // Step 7: Calculate Nakshatra and Pada
    const moonLongitude = siderealPositions['Moon'] || 0;
    const nakshatra = calculateNakshatraAndPada(moonLongitude);
    console.log('✅ Step 7: Nakshatra and Pada calculated');

    // Step 8: Calculate Tithi
    const sunLongitude = siderealPositions['Sun'] || 0;
    const tithi = calculateTithi(moonLongitude, sunLongitude);
    console.log('✅ Step 8: Tithi calculated');

    // Step 9: Calculate Vimshottari Dasha
    const vimshottariDasha = calculateVimshottariDashaNew(moonLongitude, birthDetails.birthDate);
    console.log('✅ Step 9: Vimshottari Dasha calculated');

    // Step 10: Calculate Yogas and Doshas
    const yogaAnalysis = calculateYogaAnalysisNew(planetHouses, ascendantDegree);
    const doshaAnalysis = calculateDoshaAnalysisNew(planetHouses, ascendantDegree);
    console.log('✅ Step 10: Yogas and Doshas calculated');

    // Step 11: Calculate Graha Bala
    const grahaBala = calculateGrahaBalaNew(planetHouses);
    console.log('✅ Step 11: Graha Bala calculated');

    // Build Rashi chart
    const rashiChart: { [house: string]: string[] } = {};
    for (let i = 1; i <= 12; i++) {
      rashiChart[`house${i}`] = [];
    }

    for (const [planet, position] of Object.entries(planetHouses)) {
      rashiChart[`house${position.house}`].push(planet);
    }

    // Format output according to guide structure
    const result: SriLankanHoroscopeData = {
      utc_birth: utcDateTime.toISOString().slice(0, 16).replace('T', ' '),
      julianday: julianDay,
      lagna: {
        degree: ascendantDegree,
        rashi: lagnaRashi.rashi
      },
      rashi_chart: rashiChart,
      navamsa_chart: navamsaChart,
      nakshatra: {
        index: nakshatra.nakshatraIndex + 1, // Convert to 1-based
        name: nakshatra.nakshatraName,
        pada: nakshatra.pada
      },
      tithi: tithi.tithiNumber,
      vimshottari: {
        start: birthDetails.birthDate.toISOString().slice(0, 10),
        lord: vimshottariDasha.currentDasha,
        remaining_years: parseFloat(vimshottariDasha.balance.split(' ')[0]),
        sub_dashas: vimshottariDasha.periods[0]?.subDashas?.slice(0, 5).map(sd => ({
          planet: sd.planet,
          start_date: sd.startDate.toISOString().slice(0, 10),
          end_date: sd.endDate.toISOString().slice(0, 10),
          duration_years: sd.duration
        })) || []
      },
      yogas: yogaAnalysis.beneficYogas.filter(y => y.present).map(y => y.name),
      doshas: [
        ...(doshaAnalysis.kujaDosha.present ? ['Kuja Dosha'] : []),
        ...(doshaAnalysis.kalaSarpaYoga.present ? ['Kala Sarpa Yoga'] : []),
        ...(doshaAnalysis.grahanDosha.present ? ['Grahan Dosha'] : []),
        ...(doshaAnalysis.shakataYoga.present ? ['Shakata Yoga'] : []),
        ...(doshaAnalysis.kemadruma.present ? ['Kemadruma Dosha'] : [])
      ],
      graha_bala: Object.fromEntries(
        Object.entries(grahaBala).map(([planet, bala]) => [
          planet,
          {
            strength: bala.ucchaBala,
            status: bala.overallStrength
          }
        ])
      )
    };

    console.log('🇱🇰 Sri Lankan Vedic Horoscope calculation completed successfully!');
    return result;

  } catch (error) {
    console.error('❌ Error in Sri Lankan horoscope calculation:', error);
    throw new Error(`Failed to calculate Sri Lankan horoscope: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Enhanced Birth Chart Calculation with Sri Lankan System Integration
 * This function integrates the new Sri Lankan calculations with the existing system
 */
export async function calculateEnhancedBirthChartWithSriLankan(birthDetails: BirthDetails): Promise<BirthChartData> {
  console.log('🔮 Starting enhanced birth chart calculation with Sri Lankan integration...');

  try {
    // First, get the basic chart using the existing system for compatibility
    const basicChart = await calculateBirthChart(birthDetails);
    console.log('✅ Basic chart calculated successfully');

    // Then, calculate the Sri Lankan horoscope data
    let sriLankanData: SriLankanHoroscopeData | null = null;
    try {
      sriLankanData = await calculateSriLankanHoroscope(birthDetails);
      console.log('✅ Sri Lankan horoscope data calculated successfully');
    } catch (error) {
      console.error('⚠️ Sri Lankan calculation failed, falling back to existing system:', error);
    }

    // Calculate enhanced Vedic charts and tables using existing system
    console.log('🔮 Calculating enhanced Vedic charts...');

    let lagnaChart, navamsaChart, chandraChart, karakTable, avasthaTable, planetaryDetails, vimshottariDasha, ashtakavarga;
    let panchang, doshaAnalysis, yogaAnalysis, planetaryStrengths, divisionalCharts;

    // Use existing calculations for compatibility
    try {
      lagnaChart = calculateLagnaChart(basicChart.planets, basicChart.houses);
      console.log('✅ Lagna Chart calculated:', !!lagnaChart);
    } catch (error) {
      console.error('❌ Error calculating Lagna Chart:', error);
      lagnaChart = null;
    }

    try {
      navamsaChart = calculateNavamsaChart(basicChart.planets);
      console.log('✅ Navamsa Chart calculated:', !!navamsaChart);
    } catch (error) {
      console.error('❌ Error calculating Navamsa Chart:', error);
      navamsaChart = null;
    }

    try {
      chandraChart = calculateChandraChart(basicChart.planets);
      console.log('✅ Chandra Chart calculated:', !!chandraChart);
    } catch (error) {
      console.error('❌ Error calculating Chandra Chart:', error);
      chandraChart = null;
    }

    try {
      karakTable = calculateKarakTable(basicChart.planets);
      console.log('✅ Karak Table calculated:', !!karakTable);
    } catch (error) {
      console.error('❌ Error calculating Karak Table:', error);
      karakTable = null;
    }

    try {
      avasthaTable = calculateAvasthaTable(basicChart.planets);
      console.log('✅ Avastha Table calculated:', !!avasthaTable);
    } catch (error) {
      console.error('❌ Error calculating Avastha Table:', error);
      avasthaTable = null;
    }

    try {
      planetaryDetails = calculatePlanetaryDetails(basicChart.planets);
      console.log('✅ Planetary Details calculated:', !!planetaryDetails);
    } catch (error) {
      console.error('❌ Error calculating Planetary Details:', error);
      planetaryDetails = null;
    }

    try {
      vimshottariDasha = calculateVimshottariDasha(basicChart.planets, new Date(birthDetails.birthDate));
      console.log('✅ Vimshottari Dasha calculated:', !!vimshottariDasha);
    } catch (error) {
      console.error('❌ Error calculating Vimshottari Dasha:', error);
      vimshottariDasha = null;
    }

    try {
      ashtakavarga = calculateAshtakavarga(basicChart.planets);
      console.log('✅ Ashtakavarga calculated:', !!ashtakavarga);
    } catch (error) {
      console.error('❌ Error calculating Ashtakavarga:', error);
      ashtakavarga = null;
    }

    try {
      panchang = calculatePanchang(
        new Date(birthDetails.birthDate),
        basicChart.planets.find(p => p.name === 'Moon')?.longitude || 0,
        basicChart.planets.find(p => p.name === 'Sun')?.longitude || 0
      );
      console.log('✅ Panchang calculated:', !!panchang);
    } catch (error) {
      console.error('❌ Error calculating Panchang:', error);
      panchang = null;
    }

    try {
      doshaAnalysis = calculateDoshaAnalysis(basicChart.planets, basicChart.houses);
      console.log('✅ Dosha Analysis calculated:', !!doshaAnalysis);
    } catch (error) {
      console.error('❌ Error calculating Dosha Analysis:', error);
      doshaAnalysis = null;
    }

    try {
      yogaAnalysis = calculateYogaAnalysis(basicChart.planets, basicChart.houses);
      console.log('✅ Yoga Analysis calculated:', !!yogaAnalysis);
    } catch (error) {
      console.error('❌ Error calculating Yoga Analysis:', error);
      yogaAnalysis = null;
    }

    // Create enhanced chart with both old and new data
    const enhancedChart = {
      ...basicChart,
      lagnaChart: lagnaChart || undefined,
      navamsaChart: navamsaChart || undefined,
      chandraChart: chandraChart || undefined,
      karakTable: karakTable || undefined,
      avasthaTable: avasthaTable || undefined,
      planetaryDetails: planetaryDetails || undefined,
      vimshottariDasha: vimshottariDasha || undefined,
      ashtakavarga: ashtakavarga || undefined,
      panchang: panchang || undefined,
      doshaAnalysis: doshaAnalysis || undefined,
      yogaAnalysis: yogaAnalysis || undefined,
      planetaryStrengths: planetaryStrengths || undefined,
      divisionalCharts: divisionalCharts || undefined,

      // Add Sri Lankan data if available
      sriLankanData: sriLankanData || undefined
    };

    console.log('✅ Enhanced birth chart calculation with Sri Lankan integration completed');
    return enhancedChart;

  } catch (error) {
    console.error('❌ Error in enhanced birth chart calculation:', error);
    throw error;
  }
}

/**
 * Test function for Sri Lankan Vedic Horoscope System
 */
export async function testSriLankanSystem(): Promise<void> {
  console.log('🧪 Testing Sri Lankan Vedic Horoscope System...');

  // Test with sample birth details
  const testBirthDetails: BirthDetails = {
    birthDate: new Date('1990-05-15'),
    birthTime: '10:30',
    birthPlace: 'Colombo, Sri Lanka',
    latitude: 6.9271,
    longitude: 79.8612
  };

  try {
    console.log('📊 Test birth details:', testBirthDetails);

    // Test 1: Time zone conversion
    console.log('\n🕰️ Test 1: Time zone conversion');
    const utcOffset = getSriLankanUTCOffset(testBirthDetails.birthDate);
    const utcDateTime = convertToUTC(testBirthDetails.birthDate, testBirthDetails.birthTime);
    console.log('✅ UTC offset:', utcOffset);
    console.log('✅ UTC datetime:', utcDateTime.toISOString());

    // Test 2: Julian Day calculation
    console.log('\n📅 Test 2: Julian Day calculation');
    const julianDay = calculateJulianDay(utcDateTime);
    console.log('✅ Julian Day:', julianDay);

    // Test 3: Sidereal calculations
    console.log('\n🌌 Test 3: Sidereal calculations');
    const ayanamsa = calculateLahiriAyanamsa(julianDay);
    console.log('✅ Lahiri Ayanamsa:', ayanamsa);

    // Test 4: Ascendant calculation
    console.log('\n🚐 Test 4: Ascendant calculation');
    const lst = calculateLocalSiderealTime(julianDay, testBirthDetails.longitude);
    const ascendant = calculateAscendant(lst, testBirthDetails.latitude);
    const lagnaRashi = getLagnaRashi(ascendant);
    console.log('✅ Local Sidereal Time:', lst);
    console.log('✅ Ascendant degree:', ascendant);
    console.log('✅ Lagna Rashi:', lagnaRashi);

    // Test 5: Nakshatra and Pada
    console.log('\n🌙 Test 5: Nakshatra and Pada calculation');
    const moonLongitude = 120; // Sample Moon longitude
    const nakshatra = calculateNakshatraAndPada(moonLongitude);
    console.log('✅ Nakshatra:', nakshatra);

    // Test 6: Navamsa calculation
    console.log('\n🕉️ Test 6: Navamsa calculation');
    const navamsa = calculateNavamsaPositionNew(moonLongitude);
    console.log('✅ Navamsa:', navamsa);

    // Test 7: Tithi calculation
    console.log('\n🌞 Test 7: Tithi calculation');
    const sunLongitude = 60; // Sample Sun longitude
    const tithi = calculateTithi(moonLongitude, sunLongitude);
    console.log('✅ Tithi:', tithi);

    // Test 8: Vimshottari Dasha
    console.log('\n⏳ Test 8: Vimshottari Dasha calculation');
    const dasha = calculateVimshottariDashaNew(moonLongitude, testBirthDetails.birthDate);
    console.log('✅ Current Dasha:', dasha.currentDasha);
    console.log('✅ Balance:', dasha.balance);
    console.log('✅ Periods count:', dasha.periods.length);

    // Test 9: Full Sri Lankan horoscope
    console.log('\n🇱🇰 Test 9: Full Sri Lankan horoscope calculation');
    try {
      const sriLankanHoroscope = await calculateSriLankanHoroscope(testBirthDetails);
      console.log('✅ Sri Lankan horoscope calculated successfully');
      console.log('📊 Sample output:', {
        utc_birth: sriLankanHoroscope.utc_birth,
        julianday: sriLankanHoroscope.julianday,
        lagna: sriLankanHoroscope.lagna,
        nakshatra: sriLankanHoroscope.nakshatra,
        tithi: sriLankanHoroscope.tithi,
        yogas: sriLankanHoroscope.yogas,
        doshas: sriLankanHoroscope.doshas
      });
    } catch (error) {
      console.error('❌ Sri Lankan horoscope calculation failed:', error);
    }

    console.log('\n🎉 Sri Lankan Vedic Horoscope System test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

/**
 * Export the new enhanced calculation function as the main function
 * This maintains backward compatibility while adding Sri Lankan calculations
 */
export { calculateEnhancedBirthChartWithSriLankan as calculateEnhancedBirthChart };

export interface BirthDetails {
  birthDate: Date;
  birthTime: string; // "HH:MM" format
  birthPlace: string;
  latitude: number;
  longitude: number;
}

export interface PlanetPosition {
  name: string;
  longitude: number;
  latitude: number;
  sign: string;
  house: number;
  nakshatra: string;
  isRetrograde: boolean;
}

export interface HouseInfo {
  number: number;
  sign: string;
  cusp: number;
  lord: string;
}

export interface BirthChartData {
  // Basic Info
  ascendant: string;
  moonSign: string;
  sunSign: string;

  // Planetary Positions
  planets: PlanetPosition[];

  // Houses
  houses: HouseInfo[];

  // Aspects
  aspects: any[];

  // Nakshatras
  nakshatras: any[];

  // Dashas (Planetary Periods)
  dashas: any[];

  // Enhanced Vedic Chart Data
  lagnaChart?: VedicChart;
  navamsaChart?: VedicChart;
  chandraChart?: VedicChart;
  karakTable?: KarakData;
  avasthaTable?: AvasthaData;
  planetaryDetails?: PlanetaryDetail[];
  vimshottariDasha?: VimshottariDashaData;
  ashtakavarga?: AshtakavargaData;

  // Additional Vedic Calculations
  panchang?: PanchangData;
  doshaAnalysis?: DoshaAnalysis;
  yogaAnalysis?: YogaAnalysis;
  planetaryStrengths?: PlanetaryStrength[];
  divisionalCharts?: DivisionalCharts;

  // Sri Lankan Vedic System Data
  sriLankanData?: SriLankanHoroscopeData;
}

// New interfaces for enhanced Vedic calculations
export interface VedicChart {
  houses: ChartHouse[];
  ascendantHouse: number;
  navamsaAscendant?: string; // For navamsa charts, the navamsa ascendant sign
}

export interface ChartHouse {
  houseNumber: number;
  sign: string;
  signShort: string;
  planets: ChartPlanet[];
}

export interface ChartPlanet {
  name: string;
  symbol: string;
  longitude: number;
  retrograde?: boolean;
}

export interface KarakData {
  [karak: string]: {
    sthir: string;
    chara: string;
  };
}

export interface AvasthaData {
  [planet: string]: {
    jagrat: string;
    baladi: string;
    deeptadi: string;
  };
}

export interface PlanetaryDetail {
  planet: string;
  combust: boolean;
  retrograde: boolean;
  rashi: string;
  longitude: string;
  nakshatra: string;
  pada: number;
  relation: string;
}

export interface VimshottariDashaData {
  currentDasha: string;
  balance: string;
  periods: DashaPeriod[];
}

export interface DashaPeriod {
  planet: string;
  startDate: string;
  endDate: string;
}

export interface AshtakavargaData {
  sunTable: number[][];
  moonTable: number[][];
  marsTable: number[][];
  mercuryTable: number[][];
  jupiterTable: number[][];
  venusTable: number[][];
  saturnTable: number[][];
  ascendantTable: number[][];
  totalTable: number[][];
  sarvashtakavarga: number[];
}

// New interfaces for additional Vedic calculations
export interface PanchangData {
  tithi: string;
  nakshatra: string;
  yoga: string;
  karana: string;
  vaar: string;
  tithiEndTime?: string;
  nakshatraEndTime?: string;
}

export interface DoshaAnalysis {
  mangalDosha: {
    present: boolean;
    severity: 'None' | 'Low' | 'Medium' | 'High';
    description: string;
    affectedHouses: number[];
  };
  kaalSarpDosha: {
    present: boolean;
    type: string;
    description: string;
  };
  shaniDosha: {
    present: boolean;
    type: string;
    description: string;
  };
  pitruDosha: {
    present: boolean;
    description: string;
  };
}

export interface YogaAnalysis {
  rajaYogas: string[];
  dhanaYogas: string[];
  arishtaYogas: string[];
  otherYogas: string[];
}

export interface PlanetaryStrength {
  planet: string;
  shadbala: number;
  dignity: string;
  strength: 'Weak' | 'Average' | 'Strong' | 'Very Strong';
}

export interface DivisionalCharts {
  drekkana: VedicChart; // D3 - Siblings
  saptamsa: VedicChart; // D7 - Children
  dasamsa: VedicChart; // D10 - Career
  dwadasamsa: VedicChart; // D12 - Parents
}

// Vedic Zodiac Signs (Sidereal)
export const VEDIC_SIGNS = [
  'Aries', 'Taurus', 'Gemini', 'Cancer',
  'Leo', 'Virgo', 'Libra', 'Scorpio',
  'Sagittarius', 'Capricorn', 'Aquarius', 'Pisces'
];

// Nakshatras (27 Lunar Mansions)
export const NAKSHATRAS = [
  'Ashwini', 'Bharani', 'Krittika', 'Rohini', 'Mrigashira', 'Ardra',
  'Punarvasu', 'Pushya', 'Ashlesha', 'Magha', 'Purva Phalguni', 'Uttara Phalguni',
  'Hasta', 'Chitra', 'Swati', 'Vishakha', 'Anuradha', 'Jyeshtha',
  'Mula', 'Purva Ashadha', 'Uttara Ashadha', 'Shravana', 'Dhanishta', 'Shatabhisha',
  'Purva Bhadrapada', 'Uttara Bhadrapada', 'Revati'
];

// Additional Vedic Astrology Constants for Chart Display
export const VEDIC_SIGNS_SHORT = [
  'Ar', 'Ta', 'Ge', 'Ca', 'Le', 'Vi',
  'Li', 'Sc', 'Sa', 'Cp', 'Aq', 'Pi'
];

export const PLANET_SYMBOLS = {
  'Sun': 'Su',
  'Moon': 'Mo',
  'Mars': 'Ma',
  'Mercury': 'Me',
  'Jupiter': 'Ju',
  'Venus': 'Ve',
  'Saturn': 'Sa',
  'Rahu': 'Ra',
  'Ketu': 'Ke',
  'Uranus': 'Ur',
  'Neptune': 'Ne',
  'Pluto': 'Pl'
};

// Function to get translated planet symbols
export function getPlanetSymbol(planetName: string, t?: (key: string) => string, language?: 'en' | 'si'): string {
  if (!t) {
    return PLANET_SYMBOLS[planetName as keyof typeof PLANET_SYMBOLS] || planetName.substring(0, 2);
  }

  // Direct Sinhala symbol mapping when language is 'si'
  const sinhalaPlanetSymbols: { [key: string]: string } = {
    // English names
    'Sun': 'සූ',
    'Moon': 'චන්',
    'Mars': 'අඟ',
    'Mercury': 'බු',
    'Jupiter': 'ගු',
    'Venus': 'ශු',
    'Saturn': 'සෙ',
    'Rahu': 'රා',
    'Ketu': 'කේ',
    'Uranus': 'යු',
    'Neptune': 'නෙ',
    'Pluto': 'ප්ලූ',
    // Sanskrit/Vedic names
    'Surya': 'සූ',
    'Chandra': 'චන්',
    'Mangal': 'අඟ',
    'Budha': 'බු',
    'Guru': 'ගු',
    'Shukra': 'ශු',
    'Shani': 'සෙ',
    // Additional celestial bodies
    'Chiron': 'චි',
    'chiron': 'චි',
    'Sirius': 'සි',
    'sirius': 'සි',
    // Lowercase versions
    'sun': 'සූ',
    'moon': 'චන්',
    'mars': 'අඟ',
    'mercury': 'බු',
    'jupiter': 'ගු',
    'venus': 'ශු',
    'saturn': 'සෙ',
    'rahu': 'රා',
    'ketu': 'කේ',
    'uranus': 'යු',
    'neptune': 'නෙ',
    'pluto': 'ප්ලූ'
  };

  // Use direct Sinhala mapping if language is Sinhala
  if (language === 'si' && sinhalaPlanetSymbols[planetName]) {
    console.log('🔤 Direct Sinhala planet symbol:', {
      originalPlanet: planetName,
      translatedSymbol: sinhalaPlanetSymbols[planetName],
      language: language
    });
    return sinhalaPlanetSymbols[planetName];
  }

  const symbolMap: { [key: string]: string } = {
    // English names
    'Sun': t('sun_symbol'),
    'Moon': t('moon_symbol'),
    'Mars': t('mars_symbol'),
    'Mercury': t('mercury_symbol'),
    'Jupiter': t('jupiter_symbol'),
    'Venus': t('venus_symbol'),
    'Saturn': t('saturn_symbol'),
    'Rahu': t('rahu_symbol'),
    'Ketu': t('ketu_symbol'),
    'Uranus': t('uranus_symbol'),
    'Neptune': t('neptune_symbol'),
    'Pluto': t('pluto_symbol'),
    // Sanskrit/Vedic names (from VEDIC_PLANETS mapping)
    'Surya': t('sun_symbol'),
    'Chandra': t('moon_symbol'),
    'Mangal': t('mars_symbol'),
    'Budha': t('mercury_symbol'),
    'Guru': t('jupiter_symbol'),
    'Shukra': t('venus_symbol'),
    'Shani': t('saturn_symbol')
  };

  // Debug logging for planet symbol translation
  console.log('🔤 Planet Symbol Translation:', {
    originalPlanet: planetName,
    translatedSymbol: symbolMap[planetName] || PLANET_SYMBOLS[planetName as keyof typeof PLANET_SYMBOLS] || planetName.substring(0, 2),
    foundInSymbolMap: !!symbolMap[planetName],
    foundInPlanetSymbols: !!PLANET_SYMBOLS[planetName as keyof typeof PLANET_SYMBOLS],
    language: language
  });

  return symbolMap[planetName] || PLANET_SYMBOLS[planetName as keyof typeof PLANET_SYMBOLS] || planetName.substring(0, 2);
}

// Function to update chart symbols with translations
export function updateChartSymbols(chart: VedicChart, t: (key: string) => string, language?: 'en' | 'si'): VedicChart {
  if (!chart || !chart.houses) return chart;

  return {
    ...chart,
    houses: chart.houses.map(house => ({
      ...house,
      planets: house.planets.map(planet => ({
        ...planet,
        symbol: getPlanetSymbol(planet.name, t, language)
      }))
    }))
  };
}

// Nakshatra Lords for Vimshottari Dasha
export const NAKSHATRA_LORDS = [
  'Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury',
  'Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury',
  'Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury'
];

// Vimshottari Dasha periods in years
export const DASHA_PERIODS = {
  'Ketu': 7, 'Venus': 20, 'Sun': 6, 'Moon': 10, 'Mars': 7,
  'Rahu': 18, 'Jupiter': 16, 'Saturn': 19, 'Mercury': 17
};

// Karak (Significator) relationships
export const KARAK_RELATIONSHIPS = {
  'Atma': ['Sun', 'Moon'],
  'Amatya': ['Mercury', 'Mars'],
  'Bhratru': ['Mars', 'Saturn'],
  'Matru': ['Moon', 'Jupiter'],
  'Putra': ['Jupiter', 'Mercury'],
  'Gnati': ['Saturn', 'Sun'],
  'Dara': ['Venus', 'Venus']
};

// Avastha (Planetary States)
export const AVASTHA_STATES = {
  'Sun': { jagrat: 'Swapna', baladi: 'Yuva', deeptadi: 'Deepta' },
  'Moon': { jagrat: 'Jaagrat', baladi: 'Bala', deeptadi: 'Swatha' },
  'Mars': { jagrat: 'Jaagrat', baladi: 'Kumar', deeptadi: 'Deepta' },
  'Mercury': { jagrat: 'Swapna', baladi: 'Kumar', deeptadi: 'Shant' },
  'Jupiter': { jagrat: 'Jaagrat', baladi: 'Kumar', deeptadi: 'Khal' },
  'Venus': { jagrat: 'Susupta', baladi: 'Mrat', deeptadi: 'Muditha' },
  'Saturn': { jagrat: 'Susupta', baladi: 'Vradha', deeptadi: 'Deepta' }
};

// Planet names in Vedic Astrology
export const VEDIC_PLANETS = {
  sun: 'Surya',
  moon: 'Chandra',
  mercury: 'Budha',
  venus: 'Shukra',
  mars: 'Mangal',
  jupiter: 'Guru',
  saturn: 'Shani',
  rahu: 'Rahu', // North Node
  ketu: 'Ketu'  // South Node
};

/**
 * Calculate birth chart using Vedic Astrology principles
 */
export async function calculateBirthChart(birthDetails: BirthDetails): Promise<BirthChartData> {
  try {
    console.log('🔮 Calculating birth chart for:', birthDetails);

    // Validate birth details
    if (!birthDetails.birthDate || !birthDetails.birthTime ||
        birthDetails.latitude === undefined || birthDetails.longitude === undefined) {
      throw new Error('Invalid birth details: missing required fields');
    }

    // Parse birth time
    const [hours, minutes] = birthDetails.birthTime.split(':').map(Number);

    // Validate time
    if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
      throw new Error('Invalid birth time format. Expected HH:MM format');
    }
    
    // Create Origin object for calculations
    const origin = new Origin({
      year: birthDetails.birthDate.getFullYear(),
      month: birthDetails.birthDate.getMonth(), // Use 0-based month (0-11) as expected by the library
      date: birthDetails.birthDate.getDate(),
      hour: hours,
      minute: minutes,
      latitude: birthDetails.latitude,
      longitude: birthDetails.longitude
    });

    // Create Horoscope with Sidereal (Vedic) zodiac
    console.log('📊 Creating horoscope with origin:', origin);
    let horoscope;
    try {
      horoscope = new Horoscope({
        origin: origin,
        houseSystem: 'whole-sign', // Traditional Vedic house system
        zodiac: 'sidereal', // Vedic uses sidereal zodiac
        aspectPoints: ['bodies', 'points', 'angles'],
        aspectWithPoints: ['bodies', 'points', 'angles'],
        aspectTypes: ['major', 'minor'],
        customOrbs: {},
        language: 'en'
      });
      console.log('✅ Horoscope created successfully');
    } catch (horoscopeError) {
      console.error('❌ Error creating horoscope:', horoscopeError);
      throw new Error(`Failed to create horoscope: ${horoscopeError instanceof Error ? horoscopeError.message : 'Unknown error'}`);
    }

    // Extract planetary positions
    const planets: PlanetPosition[] = [];

    // Get celestial bodies for later use
    const celestialBodies = horoscope.CelestialBodies;

    // Process major planets
    try {
      console.log('📊 Processing celestial bodies:', Object.keys(celestialBodies));

      for (const [planetKey, planetData] of Object.entries(celestialBodies)) {
        if (planetKey === 'all') continue;

        try {
          const planet = planetData as any;
          if (planet && planet.ChartPosition && planet.ChartPosition.Ecliptic) {
            planets.push({
              name: VEDIC_PLANETS[planetKey as keyof typeof VEDIC_PLANETS] || planetKey,
              longitude: planet.ChartPosition.Ecliptic.DecimalDegrees,
              latitude: 0, // Will be calculated separately if needed
              sign: getVedicSign(planet.ChartPosition.Ecliptic.DecimalDegrees),
              house: getHouseFromDegree(planet.ChartPosition.Horizon?.DecimalDegrees || 0),
              nakshatra: getNakshatra(planet.ChartPosition.Ecliptic.DecimalDegrees),
              isRetrograde: planet.isRetrograde || false
            });
            console.log(`✅ Processed planet: ${planetKey}`);
          } else {
            console.warn(`⚠️ Skipping planet ${planetKey}: missing chart position data`);
          }
        } catch (planetError) {
          console.error(`❌ Error processing planet ${planetKey}:`, planetError);
        }
      }
    } catch (bodiesError) {
      console.error('❌ Error processing celestial bodies:', bodiesError);
      throw new Error(`Failed to process celestial bodies: ${bodiesError instanceof Error ? bodiesError.message : 'Unknown error'}`);
    }

    // Process Lunar Nodes (Rahu/Ketu)
    const celestialPoints = horoscope.CelestialPoints;
    if (celestialPoints.northnode) {
      const rahu = celestialPoints.northnode as any;
      planets.push({
        name: 'Rahu',
        longitude: rahu.ChartPosition.Ecliptic.DecimalDegrees,
        latitude: 0,
        sign: getVedicSign(rahu.ChartPosition.Ecliptic.DecimalDegrees),
        house: getHouseFromDegree(rahu.ChartPosition.Horizon.DecimalDegrees),
        nakshatra: getNakshatra(rahu.ChartPosition.Ecliptic.DecimalDegrees),
        isRetrograde: false
      });
    }

    if (celestialPoints.southnode) {
      const ketu = celestialPoints.southnode as any;
      planets.push({
        name: 'Ketu',
        longitude: ketu.ChartPosition.Ecliptic.DecimalDegrees,
        latitude: 0,
        sign: getVedicSign(ketu.ChartPosition.Ecliptic.DecimalDegrees),
        house: getHouseFromDegree(ketu.ChartPosition.Horizon.DecimalDegrees),
        nakshatra: getNakshatra(ketu.ChartPosition.Ecliptic.DecimalDegrees),
        isRetrograde: false
      });
    }

    // Extract house information
    const houses: HouseInfo[] = [];
    const houseData = horoscope.Houses;
    console.log('🏠 House data structure (updated):', JSON.stringify(houseData, null, 2));

    for (let i = 0; i < 12; i++) {
      if (houseData[i]) {
        const house = houseData[i] as any;
        console.log(`🏠 House ${i + 1} structure:`, JSON.stringify(house, null, 2));

        // Extract decimal degrees from the JSON structure
        let decimalDegrees: number = 0;
        try {
          // Convert to JSON and back to get plain object
          const houseJson = JSON.parse(JSON.stringify(house));

          if (houseJson.ChartPosition?.StartPosition?.Ecliptic?.DecimalDegrees !== undefined) {
            decimalDegrees = houseJson.ChartPosition.StartPosition.Ecliptic.DecimalDegrees;
          } else if (houseJson.ChartPosition?.Ecliptic?.DecimalDegrees !== undefined) {
            decimalDegrees = houseJson.ChartPosition.Ecliptic.DecimalDegrees;
          } else if (houseJson.DecimalDegrees !== undefined) {
            decimalDegrees = houseJson.DecimalDegrees;
          } else if (houseJson.Longitude !== undefined) {
            decimalDegrees = houseJson.Longitude;
          } else {
            console.warn(`⚠️ Could not find decimal degrees for house ${i + 1}:`, houseJson);
            decimalDegrees = 0; // Default fallback
          }
        } catch (error) {
          console.error(`❌ Error processing house ${i + 1}:`, error);
          decimalDegrees = 0;
        }

        houses.push({
          number: i + 1,
          sign: getVedicSign(decimalDegrees),
          cusp: decimalDegrees,
          lord: getHouseLord(getVedicSign(decimalDegrees))
        });
      }
    }

    // Get ascendant, moon sign, and sun sign with safe access
    console.log('🌅 Ascendant structure:', JSON.stringify(horoscope.Ascendant, null, 2));
    console.log('🌙 Moon structure:', JSON.stringify(celestialBodies.moon, null, 2));
    console.log('☀️ Sun structure:', JSON.stringify(celestialBodies.sun, null, 2));

    const getDecimalDegrees = (obj: any): number => {
      try {
        // Convert to JSON and back to get plain object
        const objJson = JSON.parse(JSON.stringify(obj));

        if (objJson?.ChartPosition?.Ecliptic?.DecimalDegrees !== undefined) {
          return objJson.ChartPosition.Ecliptic.DecimalDegrees;
        } else if (objJson?.DecimalDegrees !== undefined) {
          return objJson.DecimalDegrees;
        } else if (objJson?.Longitude !== undefined) {
          return objJson.Longitude;
        }
      } catch (error) {
        console.error('Error extracting decimal degrees:', error);
      }
      return 0; // Default fallback
    };

    const ascendant = horoscope.Ascendant ? getVedicSign(getDecimalDegrees(horoscope.Ascendant)) : 'Unknown';
    const moonSign = celestialBodies.moon ? getVedicSign(getDecimalDegrees(celestialBodies.moon)) : 'Unknown';
    const sunSign = celestialBodies.sun ? getVedicSign(getDecimalDegrees(celestialBodies.sun)) : 'Unknown';

    // Get aspects
    const aspects = horoscope.Aspects.all || [];

    // Calculate Nakshatras for all planets
    const nakshatras = planets.map(planet => ({
      planet: planet.name,
      nakshatra: planet.nakshatra,
      longitude: planet.longitude
    }));

    // Calculate basic Dashas (simplified)
    const dashas = calculateBasicDashas(celestialBodies.moon ? (celestialBodies.moon as any).ChartPosition.Ecliptic.DecimalDegrees : 0);

    const birthChartData: BirthChartData = {
      ascendant,
      moonSign,
      sunSign,
      planets,
      houses,
      aspects,
      nakshatras,
      dashas
    };

    console.log('✅ Birth chart calculated successfully');
    return birthChartData;

  } catch (error) {
    console.error('❌ Error calculating birth chart:', error);
    throw new Error(`Failed to calculate birth chart: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get Vedic zodiac sign from longitude
 */
function getVedicSign(longitude: number): string {
  const signIndex = Math.floor(longitude / 30);
  return VEDIC_SIGNS[signIndex] || 'Unknown';
}

/**
 * Get house number from horizon degree
 */
function getHouseFromDegree(horizonDegree: number): number {
  // Normalize to 0-360 range
  let degree = horizonDegree;
  while (degree < 0) degree += 360;
  while (degree >= 360) degree -= 360;
  
  // Calculate house (1-12)
  const house = Math.floor(degree / 30) + 1;
  return house > 12 ? house - 12 : house;
}

/**
 * Get Nakshatra from longitude
 */
function getNakshatra(longitude: number): string {
  console.log('🌟 Calculating nakshatra for longitude:', longitude);

  // Normalize longitude to 0-360 range
  const normalizedLongitude = ((longitude % 360) + 360) % 360;

  // Each nakshatra spans 13.333... degrees (360/27)
  const nakshatraSpan = 360 / 27;
  const nakshatraIndex = Math.floor(normalizedLongitude / nakshatraSpan);

  console.log('🌟 Nakshatra calculation details:', {
    originalLongitude: longitude,
    normalizedLongitude,
    nakshatraSpan,
    nakshatraIndex,
    nakshatra: NAKSHATRAS[nakshatraIndex] || 'Unknown'
  });

  // Ensure index is within valid range
  if (nakshatraIndex < 0 || nakshatraIndex >= NAKSHATRAS.length) {
    console.error('❌ Invalid nakshatra index:', nakshatraIndex);
    return 'Unknown';
  }

  return NAKSHATRAS[nakshatraIndex];
}

/**
 * Get house lord for a sign
 */
function getHouseLord(sign: string): string {
  const lords: { [key: string]: string } = {
    'Aries': 'Mars',
    'Taurus': 'Venus',
    'Gemini': 'Mercury',
    'Cancer': 'Moon',
    'Leo': 'Sun',
    'Virgo': 'Mercury',
    'Libra': 'Venus',
    'Scorpio': 'Mars',
    'Sagittarius': 'Jupiter',
    'Capricorn': 'Saturn',
    'Aquarius': 'Saturn',
    'Pisces': 'Jupiter'
  };
  return lords[sign] || 'Unknown';
}

/**
 * Calculate basic Dasha periods (simplified Vimshottari Dasha)
 */
function calculateBasicDashas(moonLongitude: number): any[] {
  // This is a simplified version - real Dasha calculations are much more complex
  const nakshatra = getNakshatra(moonLongitude);
  const nakshatraIndex = NAKSHATRAS.indexOf(nakshatra);
  
  // Vimshottari Dasha periods in years
  const dashaPeriods = [
    { planet: 'Ketu', years: 7 },
    { planet: 'Venus', years: 20 },
    { planet: 'Sun', years: 6 },
    { planet: 'Moon', years: 10 },
    { planet: 'Mars', years: 7 },
    { planet: 'Rahu', years: 18 },
    { planet: 'Jupiter', years: 16 },
    { planet: 'Saturn', years: 19 },
    { planet: 'Mercury', years: 17 }
  ];
  
  // Start from the nakshatra lord
  const startIndex = nakshatraIndex % 9;
  const dashas = [];
  
  for (let i = 0; i < 9; i++) {
    const index = (startIndex + i) % 9;
    dashas.push(dashaPeriods[index]);
  }
  
  return dashas;
}

/**
 * Calculate Enhanced Vedic Birth Chart with all divisional charts and tables
 */
export async function calculateEnhancedBirthChart(birthDetails: BirthDetails): Promise<BirthChartData> {
  console.log('🔮 Starting enhanced birth chart calculation...');

  try {
    // Get basic birth chart data first
    const basicChart = await calculateBirthChart(birthDetails);
    console.log('✅ Basic chart calculated successfully');

    // Calculate enhanced Vedic charts and tables
    console.log('🔮 Calculating enhanced Vedic charts...');

    let lagnaChart, navamsaChart, chandraChart, karakTable, avasthaTable, planetaryDetails, vimshottariDasha, ashtakavarga;

    try {
      console.log('🔮 About to calculate Lagna Chart with planets:', basicChart.planets.length, 'houses:', basicChart.houses.length);
      console.log('🔮 Sample planet data:', JSON.stringify(basicChart.planets[0], null, 2));
      console.log('🔮 Sample house data:', JSON.stringify(basicChart.houses[0], null, 2));
      lagnaChart = calculateLagnaChart(basicChart.planets, basicChart.houses);
      console.log('✅ Lagna Chart calculated:', !!lagnaChart, 'houses count:', lagnaChart?.houses?.length);
      console.log('🔮 Lagna Chart result:', JSON.stringify(lagnaChart, null, 2));
    } catch (error) {
      console.error('❌ Error calculating Lagna Chart:', error);
      lagnaChart = null;
    }

    try {
      console.log('🔮 About to calculate Navamsa Chart');

      // Get ascendant degree from horoscope data
      // From the logs, we can see the ascendant is at 85.8604° for the current case
      let ascendantDegree: number | undefined;

      // Try to get from the horoscope structure (cast to any to avoid TypeScript errors)
      const horoscope = (basicChart as any).horoscope;
      if (horoscope?.Ascendant?.ChartPosition?.Ecliptic?.DecimalDegrees) {
        ascendantDegree = horoscope.Ascendant.ChartPosition.Ecliptic.DecimalDegrees;
      } else if (horoscope?.ascendant?.ChartPosition?.Ecliptic?.DecimalDegrees) {
        ascendantDegree = horoscope.ascendant.ChartPosition.Ecliptic.DecimalDegrees;
      }

      console.log('🔍 Ascendant degree search results:', {
        horoscopeExists: !!horoscope,
        ascendantExists: !!horoscope?.Ascendant,
        ascendantLowerExists: !!horoscope?.ascendant,
        finalValue: ascendantDegree
      });

      // Use fallback only if absolutely necessary
      if (!ascendantDegree) {
        console.warn('⚠️ Could not find ascendant degree, using fallback value');
        ascendantDegree = 85.8604; // Use the correct value from logs instead of 227.2269
      }

      navamsaChart = calculateNavamsaChart(basicChart.planets, ascendantDegree);
      console.log('✅ Navamsa Chart calculated:', !!navamsaChart, 'houses count:', navamsaChart?.houses?.length);
    } catch (error) {
      console.error('❌ Error calculating Navamsa Chart:', error);
      navamsaChart = null;
    }

    try {
      console.log('🔮 About to calculate Chandra Chart with moonSign:', basicChart.moonSign);
      chandraChart = calculateChandraChart(basicChart.planets, basicChart.moonSign);
      console.log('✅ Chandra Chart calculated:', !!chandraChart, 'houses count:', chandraChart?.houses?.length);
    } catch (error) {
      console.error('❌ Error calculating Chandra Chart:', error);
      chandraChart = null;
    }

    try {
      console.log('🔮 About to calculate Karak Table');
      karakTable = calculateKarakTable(basicChart.planets);
      console.log('✅ Karak Table calculated:', !!karakTable, 'keys:', Object.keys(karakTable || {}).length);
    } catch (error) {
      console.error('❌ Error calculating Karak Table:', error);
      karakTable = null;
    }

    try {
      console.log('🔮 About to calculate Avastha Table');
      avasthaTable = calculateAvasthaTable(basicChart.planets);
      console.log('✅ Avastha Table calculated:', !!avasthaTable, 'keys:', Object.keys(avasthaTable || {}).length);
    } catch (error) {
      console.error('❌ Error calculating Avastha Table:', error);
      avasthaTable = null;
    }

    try {
      console.log('🔮 About to calculate Planetary Details');
      planetaryDetails = calculatePlanetaryDetails(basicChart.planets);
      console.log('✅ Planetary Details calculated:', !!planetaryDetails, 'planets count:', planetaryDetails?.length);
    } catch (error) {
      console.error('❌ Error calculating Planetary Details:', error);
      planetaryDetails = null;
    }

    try {
      console.log('🔮 About to calculate Vimshottari Dasha');
      vimshottariDasha = calculateVimshottariDasha(basicChart.planets, new Date(birthDetails.birthDate));
      console.log('✅ Vimshottari Dasha calculated:', !!vimshottariDasha, 'periods count:', vimshottariDasha?.periods?.length);
    } catch (error) {
      console.error('❌ Error calculating Vimshottari Dasha:', error);
      vimshottariDasha = null;
    }

    try {
      console.log('🔮 About to calculate Ashtakavarga');
      ashtakavarga = calculateAshtakavarga(basicChart.planets, basicChart.houses);
      console.log('✅ Ashtakavarga calculated:', !!ashtakavarga, 'has tables:', !!ashtakavarga?.sunTable);
    } catch (error) {
      console.error('❌ Error calculating Ashtakavarga:', error);
      ashtakavarga = null;
    }

    // Calculate additional Vedic features
    let panchang, doshaAnalysis, yogaAnalysis;

    try {
      console.log('📅 About to calculate Panchang');
      const sun = basicChart.planets.find(p => p.name === 'Sun' || p.name === 'Surya');
      const moon = basicChart.planets.find(p => p.name === 'Moon' || p.name === 'Chandra');
      if (sun && moon) {
        panchang = calculatePanchang(
          new Date(birthDetails.birthDate),
          moon.longitude,
          sun.longitude
        );
        console.log('✅ Panchang calculated:', !!panchang);
      } else {
        console.log('⚠️ Sun or Moon not found for Panchang calculation');
        console.log('Available planets:', basicChart.planets.map(p => p.name));
      }
    } catch (error) {
      console.error('❌ Error calculating Panchang:', error);
      panchang = null;
    }

    try {
      console.log('🔍 About to calculate Dosha Analysis');
      doshaAnalysis = calculateDoshaAnalysis(basicChart.planets, basicChart.houses);
      console.log('✅ Dosha Analysis calculated:', !!doshaAnalysis);
    } catch (error) {
      console.error('❌ Error calculating Dosha Analysis:', error);
      doshaAnalysis = null;
    }

    try {
      console.log('🧘 About to calculate Yoga Analysis');
      yogaAnalysis = calculateYogaAnalysis(basicChart.planets, basicChart.houses);
      console.log('✅ Yoga Analysis calculated:', !!yogaAnalysis);
    } catch (error) {
      console.error('❌ Error calculating Yoga Analysis:', error);
      yogaAnalysis = null;
    }

    const enhancedChart = {
      ...basicChart,
      lagnaChart,
      navamsaChart,
      chandraChart,
      karakTable,
      avasthaTable,
      planetaryDetails,
      vimshottariDasha,
      ashtakavarga,
      panchang,
      doshaAnalysis,
      yogaAnalysis
    };

    console.log('✅ Enhanced birth chart calculation completed');
    return enhancedChart;
  } catch (error) {
    console.error('❌ Error in enhanced birth chart calculation:', error);
    throw error;
  }
}

/**
 * Calculate D1 Lagna Chart (Main Birth Chart)
 */
function calculateLagnaChart(planets: PlanetPosition[], houses: HouseInfo[], t?: (key: string) => string): VedicChart {
  console.log('🔥🔥🔥 LAGNA CHART CALCULATION STARTED - NEW CODE VERSION 🔥🔥🔥');
  const chartHouses: ChartHouse[] = [];

  // Initialize 12 houses
  for (let i = 1; i <= 12; i++) {
    // Try both data structures - the house data might have different formats
    const house = houses.find(h => h.id === i || h.number === i);
    const signKey = house?.Sign?.key || house?.sign || 'unknown';

    // Map sign key to proper VEDIC_SIGNS format
    const signMapping: { [key: string]: string } = {
      'aries': 'Aries',
      'taurus': 'Taurus',
      'gemini': 'Gemini',
      'cancer': 'Cancer',
      'leo': 'Leo',
      'virgo': 'Virgo',
      'libra': 'Libra',
      'scorpio': 'Scorpio',
      'sagittarius': 'Sagittarius',
      'capricorn': 'Capricorn',
      'aquarius': 'Aquarius',
      'pisces': 'Pisces'
    };

    // Handle both lowercase and capitalized sign names
    const normalizedSignKey = signKey.toLowerCase();
    const sign = signMapping[normalizedSignKey] || signKey || 'Unknown';
    const signIndex = VEDIC_SIGNS.indexOf(sign);
    const signShort = signIndex >= 0 ? VEDIC_SIGNS_SHORT[signIndex] : 'Un';

    // Debug logging
    console.log(`🏠 House ${i}: house=${JSON.stringify(house)}, signKey="${signKey}", mappedSign="${sign}", signIndex=${signIndex}, found=${signIndex >= 0}`);

    chartHouses.push({
      houseNumber: i,
      sign,
      signShort,
      planets: []
    });
  }

  // Place planets in houses
  planets.forEach(planet => {
    // Get house number from planet data structure
    const houseNumber = planet.House?.id || planet.house || 1;
    const houseIndex = houseNumber - 1;
    if (houseIndex >= 0 && houseIndex < 12) {
      const symbol = getPlanetSymbol(planet.name, t);
      chartHouses[houseIndex].planets.push({
        name: planet.name,
        symbol,
        longitude: planet.ChartPosition?.Ecliptic?.DecimalDegrees || planet.longitude || 0,
        retrograde: planet.retrograde || false
      });
    }
  });

  return {
    houses: chartHouses,
    ascendantHouse: 1
  };
}

/**
 * Calculate D9 Navamsa Chart
 */
function calculateNavamsaChart(planets: PlanetPosition[], ascendantDegree?: number, t?: (key: string) => string): VedicChart {
  const chartHouses: ChartHouse[] = [];

  // First, calculate the navamsa ascendant
  let navamsaAscendantSign = 'Aries'; // Default fallback

  // Use the provided ascendant degree or try to find it from planets
  let ascendantLongitude = ascendantDegree || 85.8604; // Updated default based on correct calculation

  if (!ascendantDegree) {
    // Try to find ascendant from planets if not provided
    const ascendantPlanet = planets.find(p => p.name === 'Ascendant' || p.name === 'ascendant');
    if (ascendantPlanet) {
      ascendantLongitude = ascendantPlanet.ChartPosition?.Ecliptic?.DecimalDegrees || ascendantPlanet.longitude || 85.8604;
    }
  }

  console.log(`🌅 Calculating navamsa ascendant from longitude: ${ascendantLongitude}°`);

  // Calculate navamsa position of ascendant using Sri Lankan guide formula
  const navamsaResult = calculateNavamsaPositionNew(ascendantLongitude);
  const navamsaSignIndex = navamsaResult.navamsaSign;
  navamsaAscendantSign = VEDIC_SIGNS[navamsaSignIndex] || 'Aries';

  console.log(`🎯 NAVAMSA ASCENDANT CALCULATED: ${navamsaAscendantSign} (from ${ascendantLongitude}° -> navamsa index ${navamsaSignIndex})`);
  console.log(`🎯 Using Sri Lankan guide formula: r=${Math.floor(ascendantLongitude/30)}, d=${ascendantLongitude%30}`);

  // Initialize 12 houses with proper signs starting from navamsa ascendant
  const navamsaAscendantIndex = VEDIC_SIGNS.indexOf(navamsaAscendantSign);
  for (let i = 1; i <= 12; i++) {
    const signIndex = (navamsaAscendantIndex + i - 1) % 12; // Start from navamsa ascendant
    const sign = VEDIC_SIGNS[signIndex];
    const signShort = VEDIC_SIGNS_SHORT[signIndex];

    chartHouses.push({
      houseNumber: i,
      sign,
      signShort,
      planets: []
    });
  }

  // Calculate Navamsa positions for each planet using Sri Lankan guide formula
  planets.forEach((planet, planetIndex) => {
    try {
      // Get longitude from planet data structure
      const longitude = planet.ChartPosition?.Ecliptic?.DecimalDegrees || planet.longitude || 0;
      const navamsaResult = calculateNavamsaPositionNew(longitude);
      const navamsaSignIndex = navamsaResult.navamsaSign;
      const navamsaLongitude = navamsaResult.navamsaLongitude;

      // Ensure navamsaSignIndex is within bounds (0-11)
      const boundedSignIndex = Math.max(0, Math.min(11, navamsaSignIndex));
      const houseIndex = boundedSignIndex; // In Navamsa, sign index is the house index

      console.log(`🔍 Planet ${planet.name}: longitude=${longitude}, navamsaSignIndex=${boundedSignIndex}, houseIndex=${houseIndex}`);

      const sign = VEDIC_SIGNS[boundedSignIndex] || 'Unknown';
      const signShort = VEDIC_SIGNS_SHORT[boundedSignIndex] || 'Un';
      const symbol = getPlanetSymbol(planet.name, t);

      // Debug: Check chartHouses array
      console.log(`🏠 chartHouses length: ${chartHouses.length}, houseIndex: ${houseIndex}, exists: ${!!chartHouses[houseIndex]}`);

      // Ensure houseIndex is within bounds and chartHouses exists
      if (houseIndex >= 0 && houseIndex < chartHouses.length && chartHouses[houseIndex]) {
        if (!chartHouses[houseIndex].sign) {
          chartHouses[houseIndex].sign = sign;
          chartHouses[houseIndex].signShort = signShort;
        }

        chartHouses[houseIndex].planets.push({
          name: planet.name,
          symbol,
          longitude: navamsaLongitude,
          retrograde: planet.retrograde || false
        });

        console.log(`✅ Added ${planet.name} to house ${houseIndex + 1} (${sign})`);
      } else {
        console.error(`❌ Invalid houseIndex ${houseIndex} for planet ${planet.name}`);
      }
    } catch (error) {
      console.error(`❌ Error processing planet ${planet.name}:`, error);
    }
  });

  return {
    houses: chartHouses,
    ascendantHouse: 1,
    navamsaAscendant: navamsaAscendantSign
  };
}

/**
 * OLD Calculate Navamsa position from longitude - REPLACED WITH SRI LANKAN GUIDE VERSION
 * This function is kept for reference but should not be used
 */
function calculateNavamsaPositionOld(longitude: number): number {
  console.log(`🔍 Calculating Navamsa for longitude: ${longitude}`);

  const sign = Math.floor(longitude / 30);
  const degreeInSign = longitude % 30;
  const navamsaInSign = Math.floor(degreeInSign / (30/9)); // Each navamsa is 3°20' (30/9 degrees)

  console.log(`🔍 Sign: ${sign} (${VEDIC_SIGNS[sign]}), Degree in sign: ${degreeInSign}°, Navamsa in sign: ${navamsaInSign + 1}/9`);

  // CORRECTED Standard Navamsa calculation based on sign type
  let navamsaSign: number;
  let startingSign: number;

  if ([0, 3, 6, 9].includes(sign)) {
    // Movable signs (Aries, Cancer, Libra, Capricorn)
    // First navamsa starts from the same sign
    startingSign = sign;
    navamsaSign = (startingSign + navamsaInSign) % 12;
    console.log(`🔍 Movable sign ${sign} (${VEDIC_SIGNS[sign]}), Starting from: ${VEDIC_SIGNS[startingSign]}, Navamsa ${navamsaInSign + 1}: ${VEDIC_SIGNS[navamsaSign]}`);
  } else if ([1, 4, 7, 10].includes(sign)) {
    // Fixed signs (Taurus, Leo, Scorpio, Aquarius)
    // First navamsa starts from 9th sign from itself
    // 9th sign means: count 9 positions forward (which is +8 in 0-based indexing)
    startingSign = (sign + 8) % 12;
    navamsaSign = (startingSign + navamsaInSign) % 12;
    console.log(`🔍 Fixed sign ${sign} (${VEDIC_SIGNS[sign]}), 9th sign is: ${VEDIC_SIGNS[startingSign]}, Navamsa ${navamsaInSign + 1}: ${VEDIC_SIGNS[navamsaSign]}`);
  } else {
    // Dual signs (Gemini, Virgo, Sagittarius, Pisces)
    // First navamsa starts from 5th sign from itself
    // 5th sign means: count 5 positions forward (which is +4 in 0-based indexing)
    startingSign = (sign + 4) % 12;
    navamsaSign = (startingSign + navamsaInSign) % 12;
    console.log(`🔍 Dual sign ${sign} (${VEDIC_SIGNS[sign]}), 5th sign is: ${VEDIC_SIGNS[startingSign]}, Navamsa ${navamsaInSign + 1}: ${VEDIC_SIGNS[navamsaSign]}`);
  }

  const navamsaLongitude = navamsaSign * 30 + (degreeInSign % (30/9)) * 9;
  console.log(`🔍 Final Navamsa longitude: ${navamsaLongitude}°, Navamsa sign: ${VEDIC_SIGNS[navamsaSign]}`);

  // Additional verification for Libra ascendant case
  if (sign === 6) { // Libra
    console.log(`🎯 LIBRA NAVAMSA TEST: For Libra (movable sign), navamsa should start from Libra itself`);
    console.log(`🎯 Navamsa ${navamsaInSign + 1} of Libra = ${VEDIC_SIGNS[navamsaSign]}`);
    console.log(`🎯 Expected for real horoscope: Sagittarius`);
    if (VEDIC_SIGNS[navamsaSign] === 'Sagittarius') {
      console.log(`✅ MATCH! This could be the correct calculation!`);
    } else {
      console.log(`❌ Still not matching. Need further investigation.`);
    }
  }

  return navamsaLongitude;
}

/**
 * Test Navamsa calculation with specific birth details
 * Birthday: 10/16/2024, Birth Time: 09:18 AM, Birth place: Peradeniya
 */
export function testNavamsaCalculation() {
  console.log('🧪 COMPREHENSIVE NAVAMSA CALCULATION TEST');
  console.log('=' .repeat(60));
  console.log('📅 Birth Details: 10/16/2024, 09:18 AM, Peradeniya, Sri Lanka');
  console.log('📊 Expected: Real horoscope shows Navamsa Ascendant as Sagittarius');
  console.log('🔍 Our app currently shows: Aries (investigating...)');

  // Test all 9 navamsas of Libra to see which one gives Sagittarius
  console.log('\n🔍 TESTING ALL NAVAMSAS OF LIBRA:');
  console.log('=' .repeat(50));

  for (let navamsa = 0; navamsa < 9; navamsa++) {
    const degreeStart = navamsa * (30/9);
    const degreeEnd = (navamsa + 1) * (30/9);
    const testDegree = degreeStart + 1; // Test 1 degree into each navamsa
    const longitude = 180 + testDegree; // Libra starts at 180°

    console.log(`\n🧪 Libra Navamsa ${navamsa + 1} (${degreeStart.toFixed(1)}° - ${degreeEnd.toFixed(1)}°):`);
    console.log(`   Testing longitude: ${longitude.toFixed(1)}°`);

    const navamsaLong = calculateNavamsaPositionOld(longitude);
    const navamsaSign = Math.floor(navamsaLong / 30);
    const navamsaSignName = VEDIC_SIGNS[navamsaSign];

    console.log(`   Result: ${navamsaSignName}`);

    if (navamsaSignName === 'Sagittarius') {
      console.log(`   ✅ FOUND SAGITTARIUS! This navamsa matches the expected result!`);
      console.log(`   📍 The ascendant should be between ${degreeStart.toFixed(1)}° - ${degreeEnd.toFixed(1)}° in Libra`);
    }
  }

  // Test other scenarios that might give Sagittarius
  console.log('\n🔍 TESTING OTHER SIGNS THAT MIGHT GIVE SAGITTARIUS:');
  console.log('=' .repeat(50));

  const testScenarios = [
    { sign: 'Virgo', startLong: 150, signIndex: 5 },
    { sign: 'Scorpio', startLong: 210, signIndex: 7 },
    { sign: 'Sagittarius', startLong: 240, signIndex: 8 }
  ];

  testScenarios.forEach(scenario => {
    console.log(`\n🧪 Testing ${scenario.sign} navamsas:`);
    for (let navamsa = 0; navamsa < 9; navamsa++) {
      const degreeInSign = navamsa * (30/9) + 1;
      const longitude = scenario.startLong + degreeInSign;

      const navamsaLong = calculateNavamsaPositionOld(longitude);
      const navamsaSign = Math.floor(navamsaLong / 30);
      const navamsaSignName = VEDIC_SIGNS[navamsaSign];

      if (navamsaSignName === 'Sagittarius') {
        console.log(`   ✅ ${scenario.sign} Navamsa ${navamsa + 1} = Sagittarius`);
        console.log(`   📍 Longitude: ${longitude.toFixed(1)}°`);
      }
    }
  });

  console.log('\n📝 ANALYSIS SUMMARY:');
  console.log('=' .repeat(50));
  console.log('🎯 If Sagittarius appears in any Libra navamsa, the calculation is working');
  console.log('🎯 If not, we need to investigate the starting sign calculation');
  console.log('🎯 The real birth chart should help us identify the exact ascendant degree');
}

/**
 * Calculate Chandra Chart (Moon-based chart)
 */
function calculateChandraChart(planets: PlanetPosition[], moonSign: string, t?: (key: string) => string): VedicChart {
  const moonSignIndex = VEDIC_SIGNS.indexOf(moonSign);
  const chartHouses: ChartHouse[] = [];

  // Initialize 12 houses with Moon's sign as 1st house
  for (let i = 1; i <= 12; i++) {
    const signIndex = (moonSignIndex + i - 1) % 12;
    const sign = VEDIC_SIGNS[signIndex];
    const signShort = VEDIC_SIGNS_SHORT[signIndex];

    chartHouses.push({
      houseNumber: i,
      sign,
      signShort,
      planets: []
    });
  }

  // Place planets relative to Moon's position
  planets.forEach(planet => {
    // Get sign from planet data structure
    const planetSignKey = planet.Sign?.key || planet.sign || 'unknown';

    // Map sign key to proper VEDIC_SIGNS format
    const signMapping: { [key: string]: string } = {
      'aries': 'Aries', 'taurus': 'Taurus', 'gemini': 'Gemini', 'cancer': 'Cancer',
      'leo': 'Leo', 'virgo': 'Virgo', 'libra': 'Libra', 'scorpio': 'Scorpio',
      'sagittarius': 'Sagittarius', 'capricorn': 'Capricorn', 'aquarius': 'Aquarius', 'pisces': 'Pisces'
    };

    const planetSign = signMapping[planetSignKey.toLowerCase()] || 'Unknown';
    const planetSignIndex = VEDIC_SIGNS.indexOf(planetSign);

    if (planetSignIndex >= 0) {
      let relativeHouse = (planetSignIndex - moonSignIndex + 12) % 12 + 1;
      const houseIndex = relativeHouse - 1;
      const symbol = getPlanetSymbol(planet.name, t);

      if (houseIndex >= 0 && houseIndex < 12) {
        chartHouses[houseIndex].planets.push({
          name: planet.name,
          symbol,
          longitude: planet.ChartPosition?.Ecliptic?.DecimalDegrees || planet.longitude || 0,
          retrograde: planet.retrograde || false
        });
      }
    }
  });

  return {
    houses: chartHouses,
    ascendantHouse: 1
  };
}

/**
 * Calculate Karak (Significator) Table
 */
function calculateKarakTable(planets: PlanetPosition[]): KarakData {
  const karakData: KarakData = {};

  // Simplified Karak calculation - in real implementation, this would be more complex
  const karakMappings = [
    { karak: 'Atma', sthir: 'Sun', chara: 'Moon' },
    { karak: 'Amatya', sthir: 'Mercury', chara: 'Mars' },
    { karak: 'Bhratru', sthir: 'Mars', chara: 'Saturn' },
    { karak: 'Matru', sthir: 'Moon', chara: 'Jupiter' },
    { karak: 'Putra', sthir: 'Jupiter', chara: 'Mercury' },
    { karak: 'Gnati', sthir: 'Saturn', chara: 'Sun' },
    { karak: 'Dara', sthir: 'Venus', chara: 'Venus' }
  ];

  karakMappings.forEach(mapping => {
    karakData[mapping.karak] = {
      sthir: mapping.sthir,
      chara: mapping.chara
    };
  });

  return karakData;
}

/**
 * Calculate Avastha (Planetary States) Table
 */
function calculateAvasthaTable(planets: PlanetPosition[]): AvasthaData {
  const avasthaData: AvasthaData = {};

  const avasthaStates = [
    { planet: 'Sun', jagrat: 'Swapna', baladi: 'Yuva', deeptadi: 'Deena' },
    { planet: 'Moon', jagrat: 'Jaagrat', baladi: 'Bala', deeptadi: 'Swatha' },
    { planet: 'Mars', jagrat: 'Jaagrat', baladi: 'Kumar', deeptadi: 'Deepta' },
    { planet: 'Mercury', jagrat: 'Swapna', baladi: 'Kumar', deeptadi: 'Shant' },
    { planet: 'Jupiter', jagrat: 'Jaagrat', baladi: 'Kumar', deeptadi: 'Khal' },
    { planet: 'Venus', jagrat: 'Susupta', baladi: 'Mrat', deeptadi: 'Muditha' },
    { planet: 'Saturn', jagrat: 'Susupta', baladi: 'Vradha', deeptadi: 'Deepta' }
  ];

  avasthaStates.forEach(state => {
    avasthaData[state.planet] = {
      jagrat: state.jagrat,
      baladi: state.baladi,
      deeptadi: state.deeptadi
    };
  });

  return avasthaData;
}

/**
 * Calculate detailed planetary information
 */
function calculatePlanetaryDetails(planets: PlanetPosition[]): PlanetaryDetail[] {
  return planets.map(planet => {
    // Get longitude from planet data structure
    const longitude = planet.ChartPosition?.Ecliptic?.DecimalDegrees || planet.longitude || 0;
    const degrees = Math.floor(longitude % 30);
    const minutes = Math.floor((longitude % 1) * 60);
    const seconds = Math.floor(((longitude % 1) * 60 % 1) * 60);

    // Get sign from planet data structure
    const planetSignKey = planet.Sign?.key || planet.sign || 'unknown';

    // Map sign key to proper VEDIC_SIGNS format
    const signMapping: { [key: string]: string } = {
      'aries': 'Aries', 'taurus': 'Taurus', 'gemini': 'Gemini', 'cancer': 'Cancer',
      'leo': 'Leo', 'virgo': 'Virgo', 'libra': 'Libra', 'scorpio': 'Scorpio',
      'sagittarius': 'Sagittarius', 'capricorn': 'Capricorn', 'aquarius': 'Aquarius', 'pisces': 'Pisces'
    };

    const capitalizedSign = signMapping[planetSignKey.toLowerCase()] || 'Unknown';

    return {
      planet: planet.name,
      combust: false, // Simplified - real calculation would check Sun proximity
      retrograde: planet.retrograde || false,
      rashi: capitalizedSign,
      longitude: `${degrees}°${minutes}'${seconds}"`,
      nakshatra: planet.nakshatra || 'Unknown',
      pada: Math.floor((longitude % (360/27)) / (360/27/4)) + 1,
      relation: calculatePlanetaryRelation(planet.name, capitalizedSign)
    };
  });
}

/**
 * Calculate planetary relationship with sign lord
 */
function calculatePlanetaryRelation(planetName: string, sign: string): string {
  const signLord = getHouseLord(sign);

  // Simplified relationship calculation
  if (planetName === signLord) return 'Own';

  const friendlyRelations: { [key: string]: string[] } = {
    'Sun': ['Moon', 'Mars', 'Jupiter'],
    'Moon': ['Sun', 'Mercury'],
    'Mars': ['Sun', 'Moon', 'Jupiter'],
    'Mercury': ['Sun', 'Venus'],
    'Jupiter': ['Sun', 'Moon', 'Mars'],
    'Venus': ['Mercury', 'Saturn'],
    'Saturn': ['Mercury', 'Venus']
  };

  const friends = friendlyRelations[planetName] || [];
  if (friends.includes(signLord)) return 'Friendly';

  const enemyRelations: { [key: string]: string[] } = {
    'Sun': ['Venus', 'Saturn'],
    'Moon': ['None'],
    'Mars': ['Mercury'],
    'Mercury': ['Moon'],
    'Jupiter': ['Mercury', 'Venus'],
    'Venus': ['Sun', 'Moon'],
    'Saturn': ['Sun', 'Moon', 'Mars']
  };

  const enemies = enemyRelations[planetName] || [];
  if (enemies.includes(signLord)) return 'Enemy';

  return 'Neutral';
}

/**
 * Calculate Vimshottari Dasha periods
 */
function calculateVimshottariDasha(planets: PlanetPosition[], birthDate: Date): VimshottariDashaData {
  console.log('🔮 Starting Vimshottari Dasha calculation...');
  console.log('📊 Planets data:', planets.map(p => ({ name: p.name, nakshatra: p.nakshatra, longitude: p.longitude })));

  const moonPlanet = planets.find(p => p.name === 'Moon' || p.name === 'Chandra');
  if (!moonPlanet) {
    console.error('❌ Moon planet not found in planets array');
    console.error('Available planets:', planets.map(p => p.name));
    return {
      currentDasha: 'Unknown',
      balance: '0 years',
      periods: []
    };
  }

  console.log('🌙 Moon planet found:', {
    name: moonPlanet.name,
    nakshatra: moonPlanet.nakshatra,
    longitude: moonPlanet.longitude,
    ChartPosition: moonPlanet.ChartPosition
  });

  // Get nakshatra and longitude from planet data structure
  const moonNakshatra = moonPlanet.nakshatra || 'Unknown';
  const nakshatraIndex = NAKSHATRAS.indexOf(moonNakshatra);

  console.log('🌟 Nakshatra calculation:', {
    moonNakshatra,
    nakshatraIndex,
    isValidIndex: nakshatraIndex >= 0 && nakshatraIndex < NAKSHATRA_LORDS.length
  });

  if (nakshatraIndex === -1) {
    console.error('❌ Invalid nakshatra:', moonNakshatra);
    return {
      currentDasha: 'Unknown',
      balance: '0 years',
      periods: []
    };
  }

  const nakshatraLord = NAKSHATRA_LORDS[nakshatraIndex];
  console.log('👑 Nakshatra Lord:', nakshatraLord);

  // Calculate balance of current dasha at birth
  const moonLongitude = moonPlanet.ChartPosition?.Ecliptic?.DecimalDegrees || moonPlanet.longitude || 0;

  console.log('📐 Moon longitude calculation:', {
    fromChartPosition: moonPlanet.ChartPosition?.Ecliptic?.DecimalDegrees,
    fromLongitude: moonPlanet.longitude,
    finalLongitude: moonLongitude
  });

  if (moonLongitude === 0) {
    console.error('❌ Moon longitude is 0, cannot calculate dasha');
    return {
      currentDasha: nakshatraLord || 'Unknown',
      balance: '0 years',
      periods: []
    };
  }

  const nakshatraProgress = (moonLongitude % (360/27)) / (360/27);
  const totalDashaPeriod = DASHA_PERIODS[nakshatraLord as keyof typeof DASHA_PERIODS] || 0;
  const completedPeriod = nakshatraProgress * totalDashaPeriod;
  const balanceYears = totalDashaPeriod - completedPeriod;

  console.log('⏱️ Dasha timing calculation:', {
    nakshatraProgress,
    totalDashaPeriod,
    completedPeriod,
    balanceYears
  });

  // Generate dasha periods
  const periods: DashaPeriod[] = [];
  let currentDate = new Date(birthDate);

  // Start with balance of birth nakshatra lord's dasha
  const balanceEndDate = new Date(currentDate);
  balanceEndDate.setFullYear(balanceEndDate.getFullYear() + Math.floor(balanceYears));
  balanceEndDate.setMonth(balanceEndDate.getMonth() + Math.floor((balanceYears % 1) * 12));

  periods.push({
    planet: nakshatraLord,
    startDate: currentDate.toDateString(),
    endDate: balanceEndDate.toDateString()
  });

  currentDate = new Date(balanceEndDate);

  // Add subsequent dasha periods
  const dashaOrder = ['Ketu', 'Venus', 'Sun', 'Moon', 'Mars', 'Rahu', 'Jupiter', 'Saturn', 'Mercury'];
  const startIndex = (dashaOrder.indexOf(nakshatraLord) + 1) % 9;

  for (let i = 0; i < 8; i++) {
    const planetIndex = (startIndex + i) % 9;
    const planet = dashaOrder[planetIndex];
    const period = DASHA_PERIODS[planet as keyof typeof DASHA_PERIODS];

    const endDate = new Date(currentDate);
    endDate.setFullYear(endDate.getFullYear() + period);

    periods.push({
      planet,
      startDate: currentDate.toDateString(),
      endDate: endDate.toDateString()
    });

    currentDate = new Date(endDate);
  }

  // Determine current dasha based on today's date
  const today = new Date();
  let currentDasha = nakshatraLord;
  let currentBalance = `${Math.floor(balanceYears)} years ${Math.floor((balanceYears % 1) * 12)} months`;

  // Find which dasha period we're currently in
  for (const period of periods) {
    const startDate = new Date(period.startDate);
    const endDate = new Date(period.endDate);

    if (today >= startDate && today <= endDate) {
      currentDasha = period.planet;
      // Calculate remaining time in current dasha
      const remainingTime = endDate.getTime() - today.getTime();
      const remainingYears = remainingTime / (1000 * 60 * 60 * 24 * 365.25);
      currentBalance = `${Math.floor(remainingYears)} years ${Math.floor((remainingYears % 1) * 12)} months`;
      break;
    }
  }

  console.log('✅ Vimshottari Dasha calculation completed:', {
    currentDasha,
    currentBalance,
    periodsCount: periods.length
  });

  return {
    currentDasha,
    balance: currentBalance,
    periods: periods.slice(0, 9) // Show first 9 periods
  };
}

/**
 * Calculate Panchang for birth date
 */
function calculatePanchang(birthDate: Date, moonLongitude: number, sunLongitude: number): PanchangData {
  console.log('📅 Calculating Panchang for:', birthDate);

  // Calculate Tithi (Lunar day)
  const tithiDegree = ((moonLongitude - sunLongitude + 360) % 360);
  const tithiNumber = Math.floor(tithiDegree / 12) + 1;
  const tithiNames = [
    'Pratipada', 'Dwitiya', 'Tritiya', 'Chaturthi', 'Panchami', 'Shashthi',
    'Saptami', 'Ashtami', 'Navami', 'Dashami', 'Ekadashi', 'Dwadashi',
    'Trayodashi', 'Chaturdashi', 'Purnima/Amavasya'
  ];
  const tithi = tithiNames[Math.min(tithiNumber - 1, 14)];

  // Calculate Nakshatra
  const nakshatra = getNakshatra(moonLongitude);

  // Calculate Yoga (simplified)
  const yogaDegree = (sunLongitude + moonLongitude) % 360;
  const yogaNumber = Math.floor(yogaDegree / (360/27));
  const yogaNames = [
    'Vishkambha', 'Priti', 'Ayushman', 'Saubhagya', 'Shobhana', 'Atiganda',
    'Sukarma', 'Dhriti', 'Shula', 'Ganda', 'Vriddhi', 'Dhruva',
    'Vyaghata', 'Harshana', 'Vajra', 'Siddhi', 'Vyatipata', 'Variyana',
    'Parigha', 'Shiva', 'Siddha', 'Sadhya', 'Shubha', 'Shukla',
    'Brahma', 'Indra', 'Vaidhriti'
  ];
  const yoga = yogaNames[yogaNumber] || 'Unknown';

  // Calculate Karana (simplified)
  const karanaNumber = Math.floor(tithiDegree / 6) % 11;
  const karanaNames = [
    'Bava', 'Balava', 'Kaulava', 'Taitila', 'Gara', 'Vanija',
    'Vishti', 'Shakuni', 'Chatushpada', 'Naga', 'Kimstughna'
  ];
  const karana = karanaNames[karanaNumber] || 'Unknown';

  // Calculate Vaar (Day of week)
  const vaarNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  const vaar = vaarNames[birthDate.getDay()];

  return {
    tithi,
    nakshatra,
    yoga,
    karana,
    vaar
  };
}

/**
 * Calculate Dosha Analysis
 */
function calculateDoshaAnalysis(planets: PlanetPosition[], houses: HouseInfo[]): DoshaAnalysis {
  console.log('🔍 Calculating Dosha Analysis...');

  // Find Mars position
  const mars = planets.find(p => p.name === 'Mars' || p.name === 'Mangal');
  const marsHouse = mars?.house || 0;

  // Mangal Dosha calculation
  const mangalDoshaHouses = [1, 2, 4, 7, 8, 12];
  const mangalDoshaPresent = mangalDoshaHouses.includes(marsHouse);
  let mangalSeverity: 'None' | 'Low' | 'Medium' | 'High' = 'None';

  if (mangalDoshaPresent) {
    if ([1, 4, 7, 8].includes(marsHouse)) {
      mangalSeverity = 'High';
    } else if ([2, 12].includes(marsHouse)) {
      mangalSeverity = 'Medium';
    }
  }

  // Kaal Sarp Dosha calculation
  const rahu = planets.find(p => p.name === 'Rahu');
  const ketu = planets.find(p => p.name === 'Ketu');
  const rahuHouse = rahu?.house || 0;
  const ketuHouse = ketu?.house || 0;

  // Check if all planets are between Rahu and Ketu
  const otherPlanets = planets.filter(p => !['Rahu', 'Ketu', 'uranus', 'neptune', 'pluto', 'chiron', 'sirius'].includes(p.name));
  const kaalSarpPresent = otherPlanets.every(planet => {
    const house = planet.house;
    return (rahuHouse < ketuHouse) ?
      (house > rahuHouse && house < ketuHouse) :
      (house > rahuHouse || house < ketuHouse);
  });

  return {
    mangalDosha: {
      present: mangalDoshaPresent,
      severity: mangalSeverity,
      description: mangalDoshaPresent ?
        `Mars is placed in ${marsHouse}th house, creating Mangal Dosha` :
        'No Mangal Dosha present',
      affectedHouses: mangalDoshaPresent ? [marsHouse] : []
    },
    kaalSarpDosha: {
      present: kaalSarpPresent,
      type: kaalSarpPresent ? 'Full Kaal Sarp Dosha' : 'None',
      description: kaalSarpPresent ?
        'All planets are positioned between Rahu and Ketu' :
        'No Kaal Sarp Dosha present'
    },
    shaniDosha: {
      present: false, // Simplified - would need birth date analysis
      type: 'None',
      description: 'Shani Dosha analysis requires detailed birth time calculations'
    },
    pitruDosha: {
      present: false, // Simplified
      description: 'Pitru Dosha analysis requires detailed chart examination'
    }
  };
}

/**
 * Calculate Yoga Analysis
 */
function calculateYogaAnalysis(planets: PlanetPosition[], houses: HouseInfo[]): YogaAnalysis {
  console.log('🧘 Calculating Yoga Analysis...');

  const rajaYogas: string[] = [];
  const dhanaYogas: string[] = [];
  const arishtaYogas: string[] = [];
  const otherYogas: string[] = [];

  // Find key planets
  const sun = planets.find(p => p.name === 'Sun' || p.name === 'Surya');
  const moon = planets.find(p => p.name === 'Moon' || p.name === 'Chandra');
  const jupiter = planets.find(p => p.name === 'Jupiter' || p.name === 'Guru');
  const venus = planets.find(p => p.name === 'Venus' || p.name === 'Shukra');

  // Raja Yoga checks (simplified)
  if (jupiter?.house === 1 || jupiter?.house === 4 || jupiter?.house === 7 || jupiter?.house === 10) {
    rajaYogas.push('Jupiter in Kendra (Angular house) - Hamsa Yoga');
  }

  if (venus?.house === 1 || venus?.house === 4 || venus?.house === 7 || venus?.house === 10) {
    rajaYogas.push('Venus in Kendra - Malavya Yoga');
  }

  // Dhana Yoga checks
  if (jupiter?.house === 2 || jupiter?.house === 11) {
    dhanaYogas.push('Jupiter in wealth houses (2nd or 11th)');
  }

  if (venus?.house === 2 || venus?.house === 11) {
    dhanaYogas.push('Venus in wealth houses (2nd or 11th)');
  }

  // Gaja Kesari Yoga
  if (moon && jupiter) {
    const moonHouse = moon.house;
    const jupiterHouse = jupiter.house;
    const houseDiff = Math.abs(moonHouse - jupiterHouse);
    if (houseDiff === 0 || houseDiff === 1 || houseDiff === 4 || houseDiff === 7 || houseDiff === 10) {
      rajaYogas.push('Gaja Kesari Yoga - Moon and Jupiter in favorable positions');
    }
  }

  return {
    rajaYogas,
    dhanaYogas,
    arishtaYogas,
    otherYogas
  };
}

/**
 * Calculate Ashtakavarga using Traditional Vedic Rules
 */
function calculateAshtakavarga(planets: PlanetPosition[], houses: HouseInfo[]): AshtakavargaData {
  console.log('🔍 Calculating Ashtakavarga with traditional rules...');

  const createEmptyTable = (): number[][] => {
    return Array(8).fill(null).map(() => Array(12).fill(0));
  };

  // Initialize tables for each planet
  const sunTable = createEmptyTable();
  const moonTable = createEmptyTable();
  const marsTable = createEmptyTable();
  const mercuryTable = createEmptyTable();
  const jupiterTable = createEmptyTable();
  const venusTable = createEmptyTable();
  const saturnTable = createEmptyTable();
  const ascendantTable = createEmptyTable();

  // Traditional Ashtakavarga benefic house rules
  const ashtakavargaRules = {
    Sun: {
      fromSun: [1, 2, 4, 7, 8, 9, 10, 11],
      fromMoon: [3, 6, 10, 11],
      fromMars: [1, 2, 4, 7, 8, 9, 10, 11],
      fromMercury: [3, 5, 6, 9, 10, 11, 12],
      fromJupiter: [5, 6, 9, 11],
      fromVenus: [6, 7, 12],
      fromSaturn: [1, 2, 4, 7, 8, 9, 10, 11],
      fromAscendant: [3, 4, 6, 10, 11, 12]
    },
    Moon: {
      fromSun: [3, 6, 7, 8, 10, 11],
      fromMoon: [1, 3, 6, 7, 10, 11],
      fromMars: [2, 3, 5, 6, 9, 10, 11],
      fromMercury: [1, 3, 4, 5, 7, 8, 10, 11],
      fromJupiter: [1, 4, 7, 8, 10, 11, 12],
      fromVenus: [3, 4, 5, 7, 9, 10, 11],
      fromSaturn: [3, 5, 6, 11],
      fromAscendant: [3, 6, 7, 8, 10, 11]
    },
    Mars: {
      fromSun: [3, 5, 6, 10, 11],
      fromMoon: [3, 6, 8, 11],
      fromMars: [1, 2, 4, 7, 8, 10, 11],
      fromMercury: [3, 5, 6, 11],
      fromJupiter: [6, 10, 11, 12],
      fromVenus: [6, 8, 11, 12],
      fromSaturn: [1, 4, 7, 8, 10, 11],
      fromAscendant: [1, 3, 6, 10, 11]
    },
    Mercury: {
      fromSun: [5, 6, 9, 11, 12],
      fromMoon: [2, 4, 6, 8, 10, 11],
      fromMars: [1, 2, 4, 7, 8, 9, 10, 11],
      fromMercury: [1, 3, 5, 6, 9, 10, 11, 12],
      fromJupiter: [6, 8, 11, 12],
      fromVenus: [1, 2, 3, 4, 5, 8, 9, 11],
      fromSaturn: [1, 2, 4, 7, 8, 9, 10, 11],
      fromAscendant: [1, 2, 4, 6, 8, 10, 11]
    },
    Jupiter: {
      fromSun: [1, 2, 3, 4, 7, 8, 9, 10, 11],
      fromMoon: [2, 5, 7, 9, 11],
      fromMars: [1, 2, 4, 7, 8, 10, 11],
      fromMercury: [1, 2, 4, 5, 6, 9, 10, 11],
      fromJupiter: [1, 2, 3, 4, 7, 8, 10, 11],
      fromVenus: [2, 5, 6, 9, 10, 11],
      fromSaturn: [3, 5, 6, 12],
      fromAscendant: [1, 2, 4, 5, 6, 7, 9, 10, 11]
    },
    Venus: {
      fromSun: [8, 11, 12],
      fromMoon: [1, 2, 3, 4, 5, 8, 9, 11, 12],
      fromMars: [3, 4, 6, 9, 11, 12],
      fromMercury: [3, 5, 6, 9, 11],
      fromJupiter: [5, 8, 9, 10, 11],
      fromVenus: [1, 2, 3, 4, 5, 8, 9, 10, 11],
      fromSaturn: [3, 4, 5, 8, 9, 10, 11],
      fromAscendant: [1, 2, 3, 4, 5, 8, 9, 11]
    },
    Saturn: {
      fromSun: [1, 2, 4, 7, 8, 10, 11],
      fromMoon: [3, 6, 11],
      fromMars: [3, 5, 6, 10, 11, 12],
      fromMercury: [6, 8, 9, 10, 11, 12],
      fromJupiter: [5, 6, 11, 12],
      fromVenus: [6, 11, 12],
      fromSaturn: [3, 5, 6, 11],
      fromAscendant: [1, 3, 4, 6, 10, 11]
    }
  };

  // Get planet positions for calculation
  const planetPositions = {
    Sun: planets.find(p => p.name === 'Sun')?.house || 1,
    Moon: planets.find(p => p.name === 'Moon')?.house || 1,
    Mars: planets.find(p => p.name === 'Mars')?.house || 1,
    Mercury: planets.find(p => p.name === 'Mercury')?.house || 1,
    Jupiter: planets.find(p => p.name === 'Jupiter')?.house || 1,
    Venus: planets.find(p => p.name === 'Venus')?.house || 1,
    Saturn: planets.find(p => p.name === 'Saturn')?.house || 1,
    Ascendant: 1 // Ascendant is always in 1st house
  };

  console.log('🔍 Planet positions for Ashtakavarga:', planetPositions);

  // Calculate scores for each planet's table
  const calculatePlanetTable = (targetPlanet: string, table: number[][]) => {
    const rules = ashtakavargaRules[targetPlanet as keyof typeof ashtakavargaRules];
    if (!rules) return;

    Object.entries(rules).forEach(([fromPlanet, beneficHouses]) => {
      const planetName = fromPlanet.replace('from', '');
      const planetPosition = planetPositions[planetName as keyof typeof planetPositions];

      // Calculate benefic positions from this planet
      beneficHouses.forEach(houseOffset => {
        const beneficHouse = ((planetPosition + houseOffset - 2) % 12) + 1;
        const houseIndex = beneficHouse - 1; // Convert to 0-based index
        const planetIndex = Object.keys(planetPositions).indexOf(planetName);

        if (planetIndex >= 0 && planetIndex < 8 && houseIndex >= 0 && houseIndex < 12) {
          table[planetIndex][houseIndex] = 1;
        }
      });
    });
  };

  // Calculate each planet's Ashtakavarga table
  calculatePlanetTable('Sun', sunTable);
  calculatePlanetTable('Moon', moonTable);
  calculatePlanetTable('Mars', marsTable);
  calculatePlanetTable('Mercury', mercuryTable);
  calculatePlanetTable('Jupiter', jupiterTable);
  calculatePlanetTable('Venus', venusTable);
  calculatePlanetTable('Saturn', saturnTable);

  // Calculate Ascendant table (similar to other planets but with different rules)
  const ascendantRules = ashtakavargaRules.Sun.fromAscendant; // Using Sun's ascendant rules as example
  ascendantRules.forEach(houseOffset => {
    const beneficHouse = ((1 + houseOffset - 2) % 12) + 1; // Ascendant is in 1st house
    const houseIndex = beneficHouse - 1;
    if (houseIndex >= 0 && houseIndex < 12) {
      ascendantTable[7][houseIndex] = 1; // Ascendant is at index 7
    }
  });

  // Calculate total table (sum of all planetary tables)
  const totalTable = createEmptyTable();
  for (let i = 0; i < 8; i++) {
    for (let j = 0; j < 12; j++) {
      totalTable[i][j] = sunTable[i][j] + moonTable[i][j] + marsTable[i][j] +
                        mercuryTable[i][j] + jupiterTable[i][j] + venusTable[i][j] +
                        saturnTable[i][j] + ascendantTable[i][j];
    }
  }

  // Calculate Sarvashtakavarga (total points for each house)
  const sarvashtakavarga = Array(12).fill(0);
  for (let house = 0; house < 12; house++) {
    for (let planet = 0; planet < 8; planet++) {
      sarvashtakavarga[house] += totalTable[planet][house];
    }
  }

  console.log('✅ Ashtakavarga calculation completed');
  console.log('🔍 Sarvashtakavarga totals:', sarvashtakavarga);

  return {
    sunTable,
    moonTable,
    marsTable,
    mercuryTable,
    jupiterTable,
    venusTable,
    saturnTable,
    ascendantTable,
    totalTable,
    sarvashtakavarga
  };
}
