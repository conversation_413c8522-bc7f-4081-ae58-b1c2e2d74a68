// Test the 1996 case by updating user data and calling API
const { PrismaClient } = require('@prisma/client');
const http = require('http');

const prisma = new PrismaClient();

async function test1996Case() {
  console.log('🧪 TESTING 2024-10-16 CASE VIA API');
  console.log('=' .repeat(60));

  try {
    // Update the test user with 2024 birth details to test the original case
    const testUser = await prisma.user.upsert({
      where: { id: 'cmd1xseqg0000pgn8783bx5ha' },
      update: {
        birthDate: new Date('2024-10-16'),
        birthTime: '09:18',
        birthLatitude: 7.2667,  // Peradeniya coordinates
        birthLongitude: 80.5913,
        birthPlace: 'Peradeniya, Sri Lanka'
      },
      create: {
        id: 'cmd1xseqg0000pgn8783bx5ha',
        email: '<EMAIL>',
        name: 'Test User 2024',
        zodiacSign: 'scorpio',
        birthDate: new Date('2024-10-16'),
        birthTime: '09:18',
        birthLatitude: 7.2667,
        birthLongitude: 80.5913,
        birthPlace: 'Peradeniya, Sri Lanka'
      }
    });

    console.log('✅ Test user updated with 2024 birth details');
    console.log('📅 Birth Date:', testUser.birthDate.toDateString());
    console.log('⏰ Birth Time:', testUser.birthTime);
    console.log('📍 Birth Place:', testUser.birthPlace);
    console.log('🎯 Expected Lagna: Scorpio');
    console.log('🎯 Expected Navamsa: Sagittarius');
    console.log('-' .repeat(60));

    // Test the birth chart API endpoint
    const postData = JSON.stringify({
      userId: 'cmd1xseqg0000pgn8783bx5ha'
    });

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/birth-chart',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const data = await new Promise((resolve, reject) => {
      const req = http.request(options, (res) => {
        let body = '';
        res.on('data', (chunk) => body += chunk);
        res.on('end', () => {
          try {
            resolve(JSON.parse(body));
          } catch (e) {
            reject(e);
          }
        });
      });
      
      req.on('error', reject);
      req.write(postData);
      req.end();
    });

    console.log('✅ Birth chart API call successful');

    const birthChart = data.data?.birthChart;
    if (!birthChart) {
      throw new Error('Birth chart data not found in response');
    }

    console.log('\n📊 BIRTH CHART RESULTS:');
    console.log('Lagna Chart Houses:');
    if (birthChart.lagnaChart?.houses) {
      birthChart.lagnaChart.houses.forEach((house, index) => {
        console.log(`  House ${index + 1} (${house.sign}):`, house.planets?.map(p => p.name).join(', ') || 'Empty');
      });
    }

    console.log('\nNavamsa Chart Houses:');
    if (birthChart.navamsaChart?.houses) {
      birthChart.navamsaChart.houses.forEach((house, index) => {
        console.log(`  House ${index + 1} (${house.sign}):`, house.planets?.map(p => p.name).join(', ') || 'Empty');
      });
    }

    // Check ascendant information
    console.log('\n🔍 ASCENDANT INFORMATION:');
    if (birthChart.lagnaChart?.houses) {
      const ascendantHouse = birthChart.lagnaChart.houses[0]; // First house is ascendant
      console.log('Lagna (Ascendant) Sign:', ascendantHouse.sign);
      console.log('Expected Lagna: Scorpio');
      console.log('✅ Lagna Match:', ascendantHouse.sign === 'Scorpio' ? 'CORRECT' : 'INCORRECT');
    }

    // Check navamsa ascendant
    if (birthChart.navamsaChart?.houses) {
      const navamsaAscendantHouse = birthChart.navamsaChart.houses[0];
      console.log('Navamsa Ascendant Sign:', navamsaAscendantHouse.sign);
      console.log('Expected Navamsa: Sagittarius');
      console.log('✅ Navamsa Match:', navamsaAscendantHouse.sign === 'Sagittarius' ? 'CORRECT' : 'INCORRECT');
    }

    // Summary
    const lagnaCorrect = birthChart.lagnaChart?.houses[0]?.sign === 'Scorpio';
    const navamsaCorrect = birthChart.navamsaChart?.houses[0]?.sign === 'Sagittarius';

    console.log('\n🎯 SUMMARY:');
    console.log('Calculated Lagna:', birthChart.lagnaChart?.houses[0]?.sign, lagnaCorrect ? '✅' : '❌');
    console.log('Calculated Navamsa:', birthChart.navamsaChart?.houses[0]?.sign, navamsaCorrect ? '✅' : '❌');

    if (!lagnaCorrect || !navamsaCorrect) {
      console.log('\n🔧 ISSUE IDENTIFIED:');
      console.log('The 2024 case is not calculating correctly.');
      console.log('Need to fix the astrology.ts implementation.');
    } else {
      console.log('\n🎉 SUCCESS:');
      console.log('Both Lagna and Navamsa are calculating correctly!');
    }

  } catch (error) {
    console.error('❌ Error in 1996 test:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

test1996Case();
